version: '3.8'

services:
  reddit-sage:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: reddit-sage-bot
    restart: unless-stopped
    
    # Environment variables
    environment:
      - PYTHONPATH=/app
      - LOG_LEVEL=INFO
      - DRY_RUN=false
    
    # Environment file for secrets
    env_file:
      - .env
    
    # Volumes for persistent data
    volumes:
      - ./data:/app/data
      - ./config:/app/config:ro
      - ./logs:/app/data/logs
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    
    # Health check
    healthcheck:
      test: ["CMD", "python", "-c", "import sys; sys.path.append('/app'); from src.main import RedditBot; bot = RedditBot(); exit(0 if bot._health_check() else 1)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
    # Network configuration
    networks:
      - reddit-sage-network
    
    # Port mapping (if web interface is enabled)
    ports:
      - "8080:8080"
    
    # Labels for management
    labels:
      - "com.reddit-sage.service=bot"
      - "com.reddit-sage.version=1.0.0"
      - "traefik.enable=false"

  # Optional: Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: reddit-sage-redis
    restart: unless-stopped
    
    # Redis configuration
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    
    # Volumes for persistence
    volumes:
      - redis-data:/data
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.1'
    
    # Health check
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 5s
      retries: 3
    
    # Network
    networks:
      - reddit-sage-network
    
    # Labels
    labels:
      - "com.reddit-sage.service=cache"

  # Optional: Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: reddit-sage-prometheus
    restart: unless-stopped
    
    # Configuration
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    
    # Command
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    
    # Ports
    ports:
      - "9090:9090"
    
    # Network
    networks:
      - reddit-sage-network
    
    # Labels
    labels:
      - "com.reddit-sage.service=monitoring"
    
    # Only start if monitoring is enabled
    profiles:
      - monitoring

  # Optional: Grafana for dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: reddit-sage-grafana
    restart: unless-stopped
    
    # Environment
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    
    # Volumes
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    
    # Ports
    ports:
      - "3000:3000"
    
    # Network
    networks:
      - reddit-sage-network
    
    # Dependencies
    depends_on:
      - prometheus
    
    # Labels
    labels:
      - "com.reddit-sage.service=dashboard"
    
    # Only start if monitoring is enabled
    profiles:
      - monitoring

# Networks
networks:
  reddit-sage-network:
    driver: bridge
    name: reddit-sage-network

# Volumes
volumes:
  redis-data:
    name: reddit-sage-redis-data
  prometheus-data:
    name: reddit-sage-prometheus-data
  grafana-data:
    name: reddit-sage-grafana-data

# Development override
# Use: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
---
version: '3.8'

# Development overrides
services:
  reddit-sage:
    build:
      target: builder
    environment:
      - LOG_LEVEL=DEBUG
      - DRY_RUN=true
    volumes:
      - .:/app
      - /app/src/__pycache__
    command: ["python", "-m", "src.main", "--verbose", "--dry-run"]
