"""
Tests for DatabaseManager module
"""

import pytest
import tempfile
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from database import DatabaseManager, CommentRecord, AnalyticsData


class TestDatabaseManager:
    """Test cases for DatabaseManager"""
    
    @pytest.fixture
    def temp_db_path(self):
        """Create temporary database file for testing"""
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_file.close()
        yield temp_file.name
        # Cleanup
        if os.path.exists(temp_file.name):
            os.unlink(temp_file.name)
    
    @pytest.fixture
    def db_manager(self, temp_db_path):
        """Create DatabaseManager instance for testing"""
        return DatabaseManager(temp_db_path)
    
    @pytest.fixture
    def sample_comment_record(self):
        """Create sample comment record for testing"""
        return CommentRecord(
            id=None,
            post_id="test123",
            subreddit="test",
            comment_id="comment123",
            comment_text="This is a test comment",
            posted_at=datetime.now(),
            post_title="Test Post",
            post_score=10,
            comment_score=5,
            success=True,
            error_message=None,
            ai_backend="groq",
            generation_time=1.5,
            relevance_score=0.8,
            sentiment_score=0.3
        )
    
    def test_initialization(self, temp_db_path):
        """Test database manager initialization"""
        db_manager = DatabaseManager(temp_db_path)
        assert db_manager.db_path == temp_db_path
        assert os.path.exists(temp_db_path)
    
    def test_database_tables_creation(self, db_manager):
        """Test that all required tables are created"""
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            # Check if tables exist
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            expected_tables = [
                'comments', 'rate_limiter_state', 'bot_config',
                'analytics_cache', 'subreddit_performance'
            ]
            
            for table in expected_tables:
                assert table in tables
    
    def test_save_comment_record(self, db_manager, sample_comment_record):
        """Test saving comment record to database"""
        record_id = db_manager.save_comment(sample_comment_record)
        
        assert isinstance(record_id, int)
        assert record_id > 0
        
        # Verify record was saved
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM comments WHERE id = ?", (record_id,))
            row = cursor.fetchone()
            
            assert row is not None
            assert row['post_id'] == sample_comment_record.post_id
            assert row['subreddit'] == sample_comment_record.subreddit
            assert row['comment_text'] == sample_comment_record.comment_text
    
    def test_update_comment_score(self, db_manager, sample_comment_record):
        """Test updating comment score"""
        # Save initial record
        record_id = db_manager.save_comment(sample_comment_record)
        
        # Update score
        new_score = 15
        db_manager.update_comment_score(sample_comment_record.comment_id, new_score)
        
        # Verify update
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT comment_score FROM comments WHERE id = ?", (record_id,))
            row = cursor.fetchone()
            
            assert row['comment_score'] == new_score
    
    def test_get_recent_comments(self, db_manager, sample_comment_record):
        """Test retrieving recent comments"""
        # Save test record
        db_manager.save_comment(sample_comment_record)
        
        # Get recent comments
        recent_comments = db_manager.get_recent_comments(hours=24)
        
        assert len(recent_comments) == 1
        assert isinstance(recent_comments[0], CommentRecord)
        assert recent_comments[0].post_id == sample_comment_record.post_id
    
    def test_get_recent_comments_with_subreddit_filter(self, db_manager, sample_comment_record):
        """Test retrieving recent comments with subreddit filter"""
        # Save test record
        db_manager.save_comment(sample_comment_record)
        
        # Get recent comments for specific subreddit
        recent_comments = db_manager.get_recent_comments(hours=24, subreddit="test")
        assert len(recent_comments) == 1
        
        # Get recent comments for different subreddit
        recent_comments = db_manager.get_recent_comments(hours=24, subreddit="other")
        assert len(recent_comments) == 0
    
    def test_get_analytics_data(self, db_manager, sample_comment_record):
        """Test analytics data generation"""
        # Save multiple test records
        for i in range(5):
            record = CommentRecord(
                id=None,
                post_id=f"test{i}",
                subreddit="test",
                comment_id=f"comment{i}",
                comment_text=f"Test comment {i}",
                posted_at=datetime.now() - timedelta(hours=i),
                post_title=f"Test Post {i}",
                post_score=10 + i,
                comment_score=i,
                success=i < 4,  # 4 successful, 1 failed
                error_message=None if i < 4 else "Test error",
                ai_backend="groq" if i < 3 else "openai",
                generation_time=1.0 + i * 0.1,
                relevance_score=0.8,
                sentiment_score=0.3
            )
            db_manager.save_comment(record)
        
        # Get analytics
        analytics = db_manager.get_analytics_data(days=1)
        
        assert isinstance(analytics, AnalyticsData)
        assert analytics.total_comments == 5
        assert analytics.successful_comments == 4
        assert analytics.success_rate == 0.8
        assert len(analytics.backend_performance) > 0
    
    def test_save_and_load_rate_limiter_state(self, db_manager):
        """Test rate limiter state persistence"""
        test_state = {
            'global_limits': {'max_per_hour': 5, 'current_count': 2},
            'subreddit_limits': {'test': {'max_per_hour': 2, 'current_count': 1}}
        }
        
        # Save state
        db_manager.save_rate_limiter_state(test_state)
        
        # Load state
        loaded_state = db_manager.load_rate_limiter_state()
        
        assert loaded_state == test_state
    
    def test_save_and_load_config(self, db_manager):
        """Test configuration persistence"""
        test_config = {'setting1': 'value1', 'setting2': 42}
        
        # Save config
        db_manager.save_config('test_key', test_config)
        
        # Load config
        loaded_config = db_manager.load_config('test_key')
        
        assert loaded_config == test_config
        
        # Test default value
        default_value = db_manager.load_config('nonexistent_key', 'default')
        assert default_value == 'default'
    
    def test_get_subreddit_stats(self, db_manager, sample_comment_record):
        """Test subreddit statistics"""
        # Save multiple records for subreddit
        for i in range(3):
            record = CommentRecord(
                id=None,
                post_id=f"test{i}",
                subreddit="test",
                comment_id=f"comment{i}",
                comment_text=f"Test comment {i}",
                posted_at=datetime.now(),
                post_title=f"Test Post {i}",
                post_score=10,
                comment_score=i + 1,
                success=True,
                error_message=None,
                ai_backend="groq",
                generation_time=1.0,
                relevance_score=0.8,
                sentiment_score=0.3
            )
            db_manager.save_comment(record)
        
        # Get stats
        stats = db_manager.get_subreddit_stats("test")
        
        assert stats['subreddit'] == "test"
        assert stats['total_comments'] == 3
        assert stats['successful_comments'] == 3
        assert stats['success_rate'] == 1.0
        assert stats['average_karma'] == 2.0  # (1+2+3)/3
    
    def test_cleanup_old_records(self, db_manager):
        """Test cleanup of old records"""
        # Save old record
        old_record = CommentRecord(
            id=None,
            post_id="old_test",
            subreddit="test",
            comment_id="old_comment",
            comment_text="Old test comment",
            posted_at=datetime.now() - timedelta(days=35),  # 35 days old
            post_title="Old Test Post",
            post_score=10,
            comment_score=5,
            success=True,
            error_message=None,
            ai_backend="groq",
            generation_time=1.0,
            relevance_score=0.8,
            sentiment_score=0.3
        )
        db_manager.save_comment(old_record)
        
        # Save recent record
        recent_record = CommentRecord(
            id=None,
            post_id="recent_test",
            subreddit="test",
            comment_id="recent_comment",
            comment_text="Recent test comment",
            posted_at=datetime.now(),
            post_title="Recent Test Post",
            post_score=10,
            comment_score=5,
            success=True,
            error_message=None,
            ai_backend="groq",
            generation_time=1.0,
            relevance_score=0.8,
            sentiment_score=0.3
        )
        db_manager.save_comment(recent_record)
        
        # Cleanup old records (keep 30 days)
        db_manager.cleanup_old_records(days=30)
        
        # Verify old record is deleted, recent record remains
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM comments")
            count = cursor.fetchone()[0]
            assert count == 1  # Only recent record should remain
    
    def test_get_database_stats(self, db_manager, sample_comment_record):
        """Test database statistics"""
        # Save test record
        db_manager.save_comment(sample_comment_record)
        
        # Get stats
        stats = db_manager.get_database_stats()
        
        assert 'total_comments' in stats
        assert 'tracked_subreddits' in stats
        assert 'database_size_mb' in stats
        assert 'database_path' in stats
        
        assert stats['total_comments'] >= 1
        assert stats['database_path'] == db_manager.db_path
    
    def test_export_data(self, db_manager, sample_comment_record):
        """Test data export functionality"""
        # Save test record
        db_manager.save_comment(sample_comment_record)
        
        # Export data to temporary file
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json') as f:
            export_file = f.name
        
        try:
            db_manager.export_data(export_file, days=30)
            
            # Verify export file exists and has content
            assert os.path.exists(export_file)
            assert os.path.getsize(export_file) > 0
            
            # Load and verify export content
            import json
            with open(export_file, 'r') as f:
                export_data = json.load(f)
            
            assert 'export_date' in export_data
            assert 'comments' in export_data
            assert 'subreddit_performance' in export_data
            assert 'analytics' in export_data
            
            assert len(export_data['comments']) >= 1
        
        finally:
            # Cleanup
            if os.path.exists(export_file):
                os.unlink(export_file)
    
    def test_subreddit_performance_tracking(self, db_manager):
        """Test subreddit performance tracking"""
        # Save comment record
        record = CommentRecord(
            id=None,
            post_id="test123",
            subreddit="test",
            comment_id="comment123",
            comment_text="Test comment",
            posted_at=datetime.now(),
            post_title="Test Post",
            post_score=10,
            comment_score=5,
            success=True,
            error_message=None,
            ai_backend="groq",
            generation_time=1.0,
            relevance_score=0.8,
            sentiment_score=0.3
        )
        db_manager.save_comment(record)
        
        # Check subreddit performance was updated
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM subreddit_performance WHERE subreddit = ?", ("test",))
            row = cursor.fetchone()
            
            assert row is not None
            assert row['total_comments'] == 1
            assert row['successful_comments'] == 1
            assert row['total_karma'] == 5
            assert row['average_karma'] == 5.0
    
    def test_context_manager_error_handling(self, db_manager):
        """Test database context manager error handling"""
        with pytest.raises(Exception):
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                # Execute invalid SQL to trigger error
                cursor.execute("INVALID SQL STATEMENT")
    
    def test_row_to_comment_record_conversion(self, db_manager, sample_comment_record):
        """Test conversion of database row to CommentRecord"""
        # Save record
        record_id = db_manager.save_comment(sample_comment_record)
        
        # Retrieve and convert
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM comments WHERE id = ?", (record_id,))
            row = cursor.fetchone()
            
            converted_record = db_manager._row_to_comment_record(row)
            
            assert isinstance(converted_record, CommentRecord)
            assert converted_record.post_id == sample_comment_record.post_id
            assert converted_record.subreddit == sample_comment_record.subreddit
            assert converted_record.success == sample_comment_record.success
    
    def test_error_handling_in_methods(self, db_manager):
        """Test error handling in various database methods"""
        # Test with corrupted database path
        corrupted_db = DatabaseManager("/invalid/path/database.db")
        
        # Methods should handle errors gracefully
        result = corrupted_db.get_recent_comments()
        assert result == [] or isinstance(result, list)
        
        analytics = corrupted_db.get_analytics_data()
        # Should return default analytics or handle error gracefully
        assert analytics is None or isinstance(analytics, AnalyticsData)
