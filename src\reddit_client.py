"""
Reddit API Client using PRAW (Python Reddit API Wrapper)

This module provides a comprehensive interface to Reddit's API with built-in
rate limiting, error handling, and safety features.
"""

import praw
import prawcore
import time
from typing import List, Dict, Optional, Generator, Any
from datetime import datetime, timedelta
from loguru import logger
import os
from dataclasses import dataclass


@dataclass
class PostData:
    """Data structure for Reddit post information"""
    id: str
    title: str
    selftext: str
    author: str
    subreddit: str
    score: int
    num_comments: int
    created_utc: float
    url: str
    is_self: bool
    over_18: bool
    locked: bool
    archived: bool
    removed: bool
    deleted: bool


class RedditClient:
    """
    Reddit API client with advanced features for bot operations
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize Reddit client with configuration
        
        Args:
            config: Configuration dictionary containing Reddit API credentials
        """
        self.config = config
        self.reddit = None
        self.last_request_time = 0
        self.request_count = 0
        self.request_window_start = time.time()
        self.max_requests_per_minute = config.get('requests_per_minute', 30)
        
        self._initialize_reddit_instance()
    
    def _initialize_reddit_instance(self):
        """Initialize PRAW Reddit instance with credentials"""
        try:
            self.reddit = praw.Reddit(
                client_id=os.getenv('REDDIT_CLIENT_ID'),
                client_secret=os.getenv('REDDIT_CLIENT_SECRET'),
                user_agent=os.getenv('REDDIT_USER_AGENT'),
                username=os.getenv('REDDIT_USERNAME'),
                password=os.getenv('REDDIT_PASSWORD')
            )
            
            # Test authentication
            user = self.reddit.user.me()
            if user:
                logger.info(f"Authenticated as Reddit user: {user.name}")
            else:
                raise Exception("Authentication failed - no user returned")

        except Exception as e:
            logger.error(f"Failed to initialize Reddit client: {e}")
            raise
    
    def _rate_limit_check(self):
        """Implement rate limiting to respect Reddit's API limits"""
        current_time = time.time()
        
        # Reset counter if window has passed
        if current_time - self.request_window_start >= 60:
            self.request_count = 0
            self.request_window_start = current_time
        
        # Check if we're hitting rate limits
        if self.request_count >= self.max_requests_per_minute:
            sleep_time = 60 - (current_time - self.request_window_start)
            if sleep_time > 0:
                logger.warning(f"Rate limit reached, sleeping for {sleep_time:.2f} seconds")
                time.sleep(sleep_time)
                self.request_count = 0
                self.request_window_start = time.time()
        
        # Minimum delay between requests
        time_since_last = current_time - self.last_request_time
        if time_since_last < 2:  # 2 second minimum delay
            time.sleep(2 - time_since_last)
        
        self.request_count += 1
        self.last_request_time = time.time()
    
    def get_subreddit_posts(self, subreddit_name: str, sort_method: str = 'new', 
                           limit: int = 10, time_filter: str = 'day') -> List[PostData]:
        """
        Get posts from a specific subreddit
        
        Args:
            subreddit_name: Name of the subreddit (without r/)
            sort_method: Sorting method ('new', 'hot', 'top', 'rising')
            limit: Maximum number of posts to retrieve
            time_filter: Time filter for 'top' sort ('hour', 'day', 'week', 'month', 'year', 'all')
        
        Returns:
            List of PostData objects
        """
        self._rate_limit_check()
        
        try:
            subreddit = self.reddit.subreddit(subreddit_name)
            posts = []
            
            if sort_method == 'new':
                submissions = subreddit.new(limit=limit)
            elif sort_method == 'hot':
                submissions = subreddit.hot(limit=limit)
            elif sort_method == 'top':
                submissions = subreddit.top(time_filter=time_filter, limit=limit)
            elif sort_method == 'rising':
                submissions = subreddit.rising(limit=limit)
            else:
                logger.warning(f"Unknown sort method: {sort_method}, defaulting to 'new'")
                submissions = subreddit.new(limit=limit)
            
            for submission in submissions:
                post_data = PostData(
                    id=submission.id,
                    title=submission.title,
                    selftext=submission.selftext,
                    author=str(submission.author) if submission.author else '[deleted]',
                    subreddit=submission.subreddit.display_name,
                    score=submission.score,
                    num_comments=submission.num_comments,
                    created_utc=submission.created_utc,
                    url=submission.url,
                    is_self=submission.is_self,
                    over_18=submission.over_18,
                    locked=submission.locked,
                    archived=submission.archived,
                    removed=submission.removed_by_category is not None,
                    deleted=submission.selftext == '[deleted]' or submission.selftext == '[removed]'
                )
                posts.append(post_data)
            
            logger.info(f"Retrieved {len(posts)} posts from r/{subreddit_name}")
            return posts
            
        except prawcore.exceptions.NotFound:
            logger.error(f"Subreddit r/{subreddit_name} not found")
            return []
        except prawcore.exceptions.Forbidden:
            logger.error(f"Access forbidden to r/{subreddit_name}")
            return []
        except Exception as e:
            logger.error(f"Error retrieving posts from r/{subreddit_name}: {e}")
            return []
    
    def post_comment(self, post_id: str, comment_text: str, dry_run: bool = False) -> bool:
        """
        Post a comment to a Reddit submission
        
        Args:
            post_id: Reddit submission ID
            comment_text: Text content of the comment
            dry_run: If True, don't actually post the comment
        
        Returns:
            True if successful, False otherwise
        """
        if dry_run:
            logger.info(f"DRY RUN: Would post comment to {post_id}: {comment_text[:100]}...")
            return True
        
        self._rate_limit_check()
        
        try:
            submission = self.reddit.submission(id=post_id)
            comment = submission.reply(comment_text)
            
            logger.info(f"Successfully posted comment {comment.id} to post {post_id}")
            return True
            
        except prawcore.exceptions.Forbidden:
            logger.error(f"Forbidden to comment on post {post_id} (banned/restricted)")
            return False
        except prawcore.exceptions.TooManyRequests:
            logger.error(f"Rate limited when commenting on post {post_id}")
            return False
        except Exception as e:
            logger.error(f"Error posting comment to {post_id}: {e}")
            return False
    
    def get_post_details(self, post_id: str) -> Optional[PostData]:
        """
        Get detailed information about a specific post
        
        Args:
            post_id: Reddit submission ID
        
        Returns:
            PostData object or None if not found
        """
        self._rate_limit_check()
        
        try:
            submission = self.reddit.submission(id=post_id)
            
            return PostData(
                id=submission.id,
                title=submission.title,
                selftext=submission.selftext,
                author=str(submission.author) if submission.author else '[deleted]',
                subreddit=submission.subreddit.display_name,
                score=submission.score,
                num_comments=submission.num_comments,
                created_utc=submission.created_utc,
                url=submission.url,
                is_self=submission.is_self,
                over_18=submission.over_18,
                locked=submission.locked,
                archived=submission.archived,
                removed=submission.removed_by_category is not None,
                deleted=submission.selftext == '[deleted]' or submission.selftext == '[removed]'
            )
            
        except Exception as e:
            logger.error(f"Error getting post details for {post_id}: {e}")
            return None
    
    def check_user_status(self) -> Dict[str, Any]:
        """
        Check current user status and account health
        
        Returns:
            Dictionary with user status information
        """
        try:
            user = self.reddit.user.me()
            
            return {
                'username': user.name,
                'comment_karma': user.comment_karma,
                'link_karma': user.link_karma,
                'account_created': user.created_utc,
                'is_verified': user.verified,
                'has_verified_email': user.has_verified_email,
                'is_suspended': user.is_suspended if hasattr(user, 'is_suspended') else False
            }
            
        except Exception as e:
            logger.error(f"Error checking user status: {e}")
            return {}
    
    def is_healthy(self) -> bool:
        """
        Check if the Reddit client is healthy and operational
        
        Returns:
            True if healthy, False otherwise
        """
        try:
            # Simple test to verify connection
            user = self.reddit.user.me()
            return user is not None
        except Exception as e:
            logger.error(f"Reddit client health check failed: {e}")
            return False
