# Reddit API
praw==7.7.1

# AI and NLP
transformers>=4.30.0
torch>=2.0.0
sentence-transformers>=2.2.0
openai>=1.0.0
groq>=0.4.0

# Data Processing
pandas>=2.0.0
numpy>=1.24.0
scikit-learn>=1.3.0
nltk>=3.8.0
textblob>=0.17.0

# Database
sqlite3  # Built-in with Python

# Configuration
PyYAML==6.0.1
python-dotenv==1.0.0

# HTTP Requests
requests==2.31.0
httpx==0.26.0

# Logging and Monitoring
loguru==0.7.2
rich==13.7.0

# Utilities
click==8.1.7
schedule==1.2.0
python-dateutil==2.8.2
pytz==2023.3

# Development and Testing
pytest==7.4.3
pytest-cov==4.1.0
black==23.12.1
flake8==6.1.0
mypy==1.8.0

# Optional: For advanced features
beautifulsoup4==4.12.2
lxml==4.9.4
