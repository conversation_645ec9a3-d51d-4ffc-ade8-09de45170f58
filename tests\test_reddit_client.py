"""
Tests for RedditClient module
"""

import pytest
import time
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

from reddit_client import RedditClient, PostData


class TestRedditClient:
    """Test cases for RedditClient"""
    
    @pytest.fixture
    def reddit_config(self):
        """Test configuration for Reddit client"""
        return {
            'requests_per_minute': 10,
            'max_comments_per_hour': 5,
            'max_comments_per_day': 20
        }
    
    @pytest.fixture
    def mock_reddit_instance(self):
        """Mock PRAW Reddit instance"""
        mock_reddit = Mock()
        mock_user = Mock()
        mock_user.name = "test_user"
        mock_reddit.user.me.return_value = mock_user
        return mock_reddit
    
    @pytest.fixture
    def reddit_client(self, reddit_config, mock_reddit_instance):
        """Create RedditClient instance for testing"""
        with patch('reddit_client.praw.Reddit', return_value=mock_reddit_instance):
            with patch.dict('os.environ', {
                'REDDIT_CLIENT_ID': 'test_id',
                'REDDIT_CLIENT_SECRET': 'test_secret',
                'REDDIT_USER_AGENT': 'test_agent',
                'REDDIT_USERNAME': 'test_user',
                'REDDIT_PASSWORD': 'test_pass'
            }):
                client = RedditClient(reddit_config)
                client.reddit = mock_reddit_instance
                return client
    
    def test_initialization_success(self, reddit_config):
        """Test successful Reddit client initialization"""
        mock_reddit = Mock()
        mock_user = Mock()
        mock_user.name = "test_user"
        mock_reddit.user.me.return_value = mock_user
        
        with patch('reddit_client.praw.Reddit', return_value=mock_reddit):
            with patch.dict('os.environ', {
                'REDDIT_CLIENT_ID': 'test_id',
                'REDDIT_CLIENT_SECRET': 'test_secret',
                'REDDIT_USER_AGENT': 'test_agent',
                'REDDIT_USERNAME': 'test_user',
                'REDDIT_PASSWORD': 'test_pass'
            }):
                client = RedditClient(reddit_config)
                assert client.reddit == mock_reddit
                assert client.max_requests_per_minute == 10
    
    def test_initialization_failure(self, reddit_config):
        """Test Reddit client initialization failure"""
        with patch('reddit_client.praw.Reddit', side_effect=Exception("Auth failed")):
            with patch.dict('os.environ', {
                'REDDIT_CLIENT_ID': 'test_id',
                'REDDIT_CLIENT_SECRET': 'test_secret',
                'REDDIT_USER_AGENT': 'test_agent',
                'REDDIT_USERNAME': 'test_user',
                'REDDIT_PASSWORD': 'test_pass'
            }):
                with pytest.raises(Exception):
                    RedditClient(reddit_config)
    
    def test_rate_limiting(self, reddit_client):
        """Test rate limiting functionality"""
        # Reset rate limiting state
        reddit_client.request_count = 0
        reddit_client.request_window_start = time.time()
        
        # Should not be rate limited initially
        start_time = time.time()
        reddit_client._rate_limit_check()
        end_time = time.time()
        
        # Should be quick (no sleep)
        assert end_time - start_time < 0.1
        assert reddit_client.request_count == 1
    
    def test_rate_limiting_with_sleep(self, reddit_client):
        """Test rate limiting with sleep when limit exceeded"""
        # Set up rate limiting state to trigger sleep
        reddit_client.request_count = reddit_client.max_requests_per_minute
        reddit_client.request_window_start = time.time()
        
        with patch('time.sleep') as mock_sleep:
            reddit_client._rate_limit_check()
            mock_sleep.assert_called_once()
    
    def test_get_subreddit_posts_success(self, reddit_client):
        """Test successful subreddit post retrieval"""
        # Mock subreddit and submissions
        mock_subreddit = Mock()
        mock_submission = Mock()
        mock_submission.id = "test123"
        mock_submission.title = "Test Post"
        mock_submission.selftext = "Test content"
        mock_submission.author.name = "test_author"
        mock_submission.subreddit.display_name = "test"
        mock_submission.score = 10
        mock_submission.num_comments = 5
        mock_submission.created_utc = time.time()
        mock_submission.url = "https://reddit.com/test"
        mock_submission.is_self = True
        mock_submission.over_18 = False
        mock_submission.locked = False
        mock_submission.archived = False
        mock_submission.removed_by_category = None
        mock_submission.selftext = "Test content"
        
        mock_subreddit.new.return_value = [mock_submission]
        reddit_client.reddit.subreddit.return_value = mock_subreddit
        
        posts = reddit_client.get_subreddit_posts("test", "new", 10)
        
        assert len(posts) == 1
        assert isinstance(posts[0], PostData)
        assert posts[0].id == "test123"
        assert posts[0].title == "Test Post"
        assert posts[0].subreddit == "test"
    
    def test_get_subreddit_posts_not_found(self, reddit_client):
        """Test subreddit not found error handling"""
        from prawcore.exceptions import NotFound
        
        reddit_client.reddit.subreddit.side_effect = NotFound(Mock())
        
        posts = reddit_client.get_subreddit_posts("nonexistent", "new", 10)
        assert posts == []
    
    def test_get_subreddit_posts_forbidden(self, reddit_client):
        """Test forbidden subreddit error handling"""
        from prawcore.exceptions import Forbidden
        
        reddit_client.reddit.subreddit.side_effect = Forbidden(Mock())
        
        posts = reddit_client.get_subreddit_posts("private", "new", 10)
        assert posts == []
    
    def test_post_comment_dry_run(self, reddit_client):
        """Test comment posting in dry run mode"""
        result = reddit_client.post_comment("test123", "Test comment", dry_run=True)
        assert result == True
    
    def test_post_comment_success(self, reddit_client):
        """Test successful comment posting"""
        mock_submission = Mock()
        mock_comment = Mock()
        mock_comment.id = "comment123"
        mock_submission.reply.return_value = mock_comment
        reddit_client.reddit.submission.return_value = mock_submission
        
        result = reddit_client.post_comment("test123", "Test comment", dry_run=False)
        assert result == True
        mock_submission.reply.assert_called_once_with("Test comment")
    
    def test_post_comment_forbidden(self, reddit_client):
        """Test comment posting forbidden error"""
        from prawcore.exceptions import Forbidden
        
        mock_submission = Mock()
        mock_submission.reply.side_effect = Forbidden(Mock())
        reddit_client.reddit.submission.return_value = mock_submission
        
        result = reddit_client.post_comment("test123", "Test comment", dry_run=False)
        assert result == False
    
    def test_post_comment_rate_limited(self, reddit_client):
        """Test comment posting rate limit error"""
        from prawcore.exceptions import TooManyRequests
        
        mock_submission = Mock()
        mock_submission.reply.side_effect = TooManyRequests(Mock())
        reddit_client.reddit.submission.return_value = mock_submission
        
        result = reddit_client.post_comment("test123", "Test comment", dry_run=False)
        assert result == False
    
    def test_get_post_details_success(self, reddit_client):
        """Test successful post details retrieval"""
        mock_submission = Mock()
        mock_submission.id = "test123"
        mock_submission.title = "Test Post"
        mock_submission.selftext = "Test content"
        mock_submission.author.name = "test_author"
        mock_submission.subreddit.display_name = "test"
        mock_submission.score = 10
        mock_submission.num_comments = 5
        mock_submission.created_utc = time.time()
        mock_submission.url = "https://reddit.com/test"
        mock_submission.is_self = True
        mock_submission.over_18 = False
        mock_submission.locked = False
        mock_submission.archived = False
        mock_submission.removed_by_category = None
        
        reddit_client.reddit.submission.return_value = mock_submission
        
        post = reddit_client.get_post_details("test123")
        
        assert isinstance(post, PostData)
        assert post.id == "test123"
        assert post.title == "Test Post"
    
    def test_get_post_details_error(self, reddit_client):
        """Test post details retrieval error handling"""
        reddit_client.reddit.submission.side_effect = Exception("API Error")
        
        post = reddit_client.get_post_details("test123")
        assert post is None
    
    def test_check_user_status_success(self, reddit_client):
        """Test successful user status check"""
        mock_user = Mock()
        mock_user.name = "test_user"
        mock_user.comment_karma = 1000
        mock_user.link_karma = 500
        mock_user.created_utc = time.time() - 86400
        mock_user.verified = True
        mock_user.has_verified_email = True
        
        reddit_client.reddit.user.me.return_value = mock_user
        
        status = reddit_client.check_user_status()
        
        assert status['username'] == "test_user"
        assert status['comment_karma'] == 1000
        assert status['link_karma'] == 500
        assert status['is_verified'] == True
    
    def test_check_user_status_error(self, reddit_client):
        """Test user status check error handling"""
        reddit_client.reddit.user.me.side_effect = Exception("API Error")
        
        status = reddit_client.check_user_status()
        assert status == {}
    
    def test_is_healthy_success(self, reddit_client):
        """Test health check success"""
        mock_user = Mock()
        mock_user.name = "test_user"
        reddit_client.reddit.user.me.return_value = mock_user
        
        assert reddit_client.is_healthy() == True
    
    def test_is_healthy_failure(self, reddit_client):
        """Test health check failure"""
        reddit_client.reddit.user.me.side_effect = Exception("API Error")
        
        assert reddit_client.is_healthy() == False
    
    def test_different_sort_methods(self, reddit_client):
        """Test different sorting methods for posts"""
        mock_subreddit = Mock()
        mock_submission = Mock()
        mock_submission.id = "test123"
        mock_submission.title = "Test Post"
        mock_submission.selftext = "Test content"
        mock_submission.author.name = "test_author"
        mock_submission.subreddit.display_name = "test"
        mock_submission.score = 10
        mock_submission.num_comments = 5
        mock_submission.created_utc = time.time()
        mock_submission.url = "https://reddit.com/test"
        mock_submission.is_self = True
        mock_submission.over_18 = False
        mock_submission.locked = False
        mock_submission.archived = False
        mock_submission.removed_by_category = None
        
        mock_subreddit.hot.return_value = [mock_submission]
        mock_subreddit.top.return_value = [mock_submission]
        mock_subreddit.rising.return_value = [mock_submission]
        reddit_client.reddit.subreddit.return_value = mock_subreddit
        
        # Test hot
        posts = reddit_client.get_subreddit_posts("test", "hot", 10)
        assert len(posts) == 1
        mock_subreddit.hot.assert_called_once()
        
        # Test top
        posts = reddit_client.get_subreddit_posts("test", "top", 10, "day")
        assert len(posts) == 1
        mock_subreddit.top.assert_called_once()
        
        # Test rising
        posts = reddit_client.get_subreddit_posts("test", "rising", 10)
        assert len(posts) == 1
        mock_subreddit.rising.assert_called_once()
    
    def test_deleted_author_handling(self, reddit_client):
        """Test handling of deleted authors"""
        mock_subreddit = Mock()
        mock_submission = Mock()
        mock_submission.id = "test123"
        mock_submission.title = "Test Post"
        mock_submission.selftext = "Test content"
        mock_submission.author = None  # Deleted author
        mock_submission.subreddit.display_name = "test"
        mock_submission.score = 10
        mock_submission.num_comments = 5
        mock_submission.created_utc = time.time()
        mock_submission.url = "https://reddit.com/test"
        mock_submission.is_self = True
        mock_submission.over_18 = False
        mock_submission.locked = False
        mock_submission.archived = False
        mock_submission.removed_by_category = None
        
        mock_subreddit.new.return_value = [mock_submission]
        reddit_client.reddit.subreddit.return_value = mock_subreddit
        
        posts = reddit_client.get_subreddit_posts("test", "new", 10)
        
        assert len(posts) == 1
        assert posts[0].author == "[deleted]"
