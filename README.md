# RedditSage - AI-Powered Reddit Auto-Comment Bot

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Code Style: Black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

An intelligent Reddit bot that automatically finds relevant topics and generates helpful, contextual comments while respecting community guidelines and avoiding spam detection.

## 🚀 Available Product Names & Domains

Here are creative product names with likely available domains:

| Product Name | Domain Suggestion | Description |
|--------------|------------------|-------------|
| **RedditSage** | `redditsage.ai` | AI-powered Reddit wisdom |
| **CommentCraft** | `commentcraft.io` | Intelligent comment generation |
| **ThreadWise** | `threadwise.dev` | Smart thread participation |
| **RedditFlow** | `redditflow.app` | Automated Reddit engagement |
| **CommentGenius** | `commentgenius.co` | AI comment assistant |
| **RedditPulse** | `redditpulse.net` | Smart community engagement |
| **ThreadMaster** | `threadmaster.pro` | Intelligent Reddit automation |
| **RedditBrain** | `redditbrain.tech` | AI-powered Reddit tool |
| **CommentSpark** | `commentspark.ai` | Intelligent comment generation |
| **RedditMind** | `redditmind.io` | Smart Reddit automation |

## 🏗️ System Architecture

```mermaid
graph TB
    A[Reddit API Client] --> B[Content Analyzer]
    B --> C[AI Comment Generator]
    C --> D[Rate Limiter]
    D --> E[Database Manager]
    E --> F[Reddit Poster]
    
    G[Configuration Manager] --> A
    G --> B
    G --> C
    G --> D
    
    H[Monitoring & Logging] --> A
    H --> B
    H --> C
    H --> D
    H --> E
    H --> F
    
    I[External APIs] --> C
    J[Local AI Models] --> C
    
    subgraph "Core Components"
        A
        B
        C
        D
        E
        F
    end
    
    subgraph "Support Systems"
        G
        H
    end
    
    subgraph "AI Backends"
        I
        J
    end
```

## 🔄 Workflow Diagram

```mermaid
flowchart TD
    A[Start Bot] --> B[Load Configuration]
    B --> C[Initialize Reddit Client]
    C --> D[Monitor Target Subreddits]
    
    D --> E{New Post Found?}
    E -->|No| F[Wait Interval]
    F --> D
    
    E -->|Yes| G[Analyze Post Content]
    G --> H{Post Relevant?}
    H -->|No| D
    
    H -->|Yes| I[Check Rate Limits]
    I --> J{Can Comment?}
    J -->|No| K[Add to Queue]
    K --> D
    
    J -->|Yes| L[Generate Comment]
    L --> M[Quality Check]
    M --> N{Comment Good?}
    N -->|No| O[Regenerate/Skip]
    O --> D
    
    N -->|Yes| P[Post Comment]
    P --> Q[Update Database]
    Q --> R[Log Activity]
    R --> D
    
    style A fill:#e1f5fe
    style P fill:#c8e6c9
    style D fill:#fff3e0
```

## 📁 Project Structure

```
reddit-auto-comment/
├── src/
│   ├── __init__.py                 # Package initialization
│   ├── main.py                     # Application entry point
│   ├── reddit_client.py            # Reddit API wrapper with PRAW
│   ├── content_analyzer.py         # Topic detection and relevance scoring
│   ├── comment_generator.py        # AI-powered comment generation
│   ├── rate_limiter.py             # Spam prevention and rate limiting
│   ├── database.py                 # SQLite database operations
│   └── config.py                   # Configuration management
├── data/
│   ├── comments.db                 # SQLite database (auto-created)
│   └── logs/                       # Application logs
├── config/
│   ├── settings.yaml               # Main bot configuration
│   ├── subreddits.yaml             # Subreddit-specific settings
│   └── prompts.yaml                # AI comment generation prompts
├── tests/
│   ├── __init__.py
│   ├── test_reddit_client.py       # Reddit client tests
│   ├── test_content_analyzer.py    # Content analyzer tests
│   └── test_comment_generator.py   # Comment generator tests
├── requirements.txt                # Python dependencies
├── .env.example                    # Environment variables template
├── .gitignore                      # Git ignore rules
├── README.md                       # This file
└── setup.py                        # Package setup script
```

## ✨ Features

- **Intelligent Topic Detection**: Advanced NLP to identify relevant discussions
- **AI-Powered Comments**: Multiple AI backends for natural comment generation
- **Smart Rate Limiting**: Sophisticated spam prevention with per-subreddit limits
- **Contextual Awareness**: Comments tailored to subreddit culture and post context
- **Multiple AI Backends**: Support for local models and free APIs
- **Comprehensive Logging**: Detailed activity tracking and monitoring
- **Flexible Configuration**: Easy customization for different use cases
- **Database Tracking**: SQLite database for comment history and analytics

## 🛠️ Installation

1. **Clone the repository**:
```bash
git clone https://github.com/HectorTa1989/reddit-auto-comment.git
cd reddit-auto-comment
```

2. **Install dependencies**:
```bash
pip install -r requirements.txt
```

3. **Set up configuration**:
```bash
cp .env.example .env
# Edit .env with your Reddit API credentials
```

4. **Configure the bot**:
```bash
# Edit config/settings.yaml for main settings
# Edit config/subreddits.yaml for target subreddits
# Edit config/prompts.yaml for comment templates
```

## 🔧 Configuration

### Reddit API Setup

1. Go to https://www.reddit.com/prefs/apps
2. Create a new application (script type)
3. Note your client ID, client secret, and user agent
4. Add credentials to `.env` file

### AI API Setup

The bot supports multiple AI backends:
- **Groq API** (Free tier available)
- **Together AI** (Free credits)
- **Local Transformers** (No API required)
- **OpenAI-compatible APIs**

## 🚀 Usage

```bash
# Run the bot
python src/main.py

# Run with specific config
python src/main.py --config config/custom_settings.yaml

# Run in dry-run mode (no actual posting)
python src/main.py --dry-run
```

## 📊 Monitoring

The bot includes comprehensive monitoring:
- Real-time activity logs
- Comment success/failure rates
- Rate limiting statistics
- Subreddit engagement metrics
- AI model performance tracking

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## ⚠️ Disclaimer

This bot is designed to be respectful and helpful. Users are responsible for:
- Following Reddit's Terms of Service
- Respecting subreddit rules
- Monitoring bot behavior
- Ensuring comments add value to discussions

## 🔗 Links

- **GitHub**: https://github.com/HectorTa1989/reddit-auto-comment
- **Documentation**: [Wiki](https://github.com/HectorTa1989/reddit-auto-comment/wiki)
- **Issues**: [Bug Reports](https://github.com/HectorTa1989/reddit-auto-comment/issues)
