"""
RedditSage - AI-Powered Reddit Auto-Comment Bot

Main application entry point that orchestrates all bot components.
"""

import asyncio
import signal
import sys
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import click
from loguru import logger
import schedule

from .config import Config
from .reddit_client import RedditClient
from .content_analyzer import ContentAnalyzer
from .comment_generator import CommentGenerator
from .rate_limiter import RateLimiter
from .database import DatabaseManager, CommentRecord


class RedditBot:
    """
    Main Reddit bot orchestrator
    """
    
    def __init__(self, config_dir: str = "config"):
        """
        Initialize the Reddit bot
        
        Args:
            config_dir: Directory containing configuration files
        """
        self.config = Config(config_dir)
        self.running = False
        self.shutdown_requested = False
        
        # Initialize components
        self.database = DatabaseManager(self.config.get_database_config().get('path', 'data/comments.db'))
        self.reddit_client = RedditClient(self.config.get_reddit_config())
        self.content_analyzer = ContentAnalyzer(self.config.get_content_analysis_config())
        self.comment_generator = CommentGenerator(self.config.get_ai_config(), self.config.prompts)
        self.rate_limiter = RateLimiter(self.config.get_rate_limiting_config(), self.database)
        
        # Setup logging
        self._setup_logging()
        
        # Setup signal handlers
        self._setup_signal_handlers()
        
        logger.info("RedditSage bot initialized successfully")
    
    def _setup_logging(self):
        """Setup logging configuration"""
        logging_config = self.config.get_logging_config()
        
        # Remove default logger
        logger.remove()
        
        # Add console logger
        if logging_config.get('enable_console_output', True):
            logger.add(
                sys.stderr,
                level=logging_config.get('level', 'INFO'),
                format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
                colorize=logging_config.get('enable_rich_formatting', True)
            )
        
        # Add file logger
        log_file = logging_config.get('file', 'data/logs/reddit_bot.log')
        if log_file:
            logger.add(
                log_file,
                level=logging_config.get('level', 'INFO'),
                format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
                rotation=f"{logging_config.get('max_file_size_mb', 10)} MB",
                retention=f"{logging_config.get('backup_count', 5)} files",
                compression="zip"
            )
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating graceful shutdown...")
            self.shutdown_requested = True
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def run(self):
        """Run the main bot loop"""
        try:
            self.running = True
            logger.info("Starting RedditSage bot...")
            
            # Perform initial health checks
            if not self._health_check():
                logger.error("Health check failed, aborting startup")
                return
            
            # Schedule periodic tasks
            self._schedule_tasks()
            
            # Main loop
            while self.running and not self.shutdown_requested:
                try:
                    # Process enabled subreddits
                    self._process_subreddits()
                    
                    # Run scheduled tasks
                    schedule.run_pending()
                    
                    # Sleep between cycles
                    check_interval = self.config.get_reddit_config().get('check_interval_seconds', 300)
                    time.sleep(check_interval)
                    
                except KeyboardInterrupt:
                    logger.info("Keyboard interrupt received")
                    break
                except Exception as e:
                    logger.error(f"Error in main loop: {e}")
                    time.sleep(60)  # Wait before retrying
            
            logger.info("Bot main loop ended")
        
        except Exception as e:
            logger.error(f"Fatal error in bot execution: {e}")
            raise
        finally:
            self._cleanup()
    
    def _health_check(self) -> bool:
        """Perform health checks on all components"""
        logger.info("Performing health checks...")
        
        # Check Reddit client
        if not self.reddit_client.is_healthy():
            logger.error("Reddit client health check failed")
            return False
        
        # Check AI backends
        backend_status = self.comment_generator.get_backend_status()
        available_backends = [name for name, status in backend_status.items() if status]
        
        if not available_backends:
            logger.error("No AI backends available")
            return False
        
        logger.info(f"Available AI backends: {available_backends}")
        
        # Check database
        try:
            db_stats = self.database.get_database_stats()
            logger.info(f"Database stats: {db_stats}")
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False
        
        logger.info("All health checks passed")
        return True
    
    def _schedule_tasks(self):
        """Schedule periodic maintenance tasks"""
        # Database cleanup
        schedule.every().day.at("02:00").do(self._cleanup_database)
        
        # Rate limiter state save
        schedule.every(10).minutes.do(self._save_rate_limiter_state)
        
        # Health monitoring
        schedule.every().hour.do(self._log_status)
        
        logger.info("Scheduled maintenance tasks")
    
    def _process_subreddits(self):
        """Process all enabled subreddits"""
        enabled_subreddits = self.config.get_enabled_subreddits()
        
        if not enabled_subreddits:
            logger.warning("No enabled subreddits found")
            return
        
        logger.info(f"Processing {len(enabled_subreddits)} subreddits")
        
        for subreddit in enabled_subreddits:
            try:
                self._process_subreddit(subreddit)
            except Exception as e:
                logger.error(f"Error processing r/{subreddit}: {e}")
                continue
    
    def _process_subreddit(self, subreddit: str):
        """
        Process a single subreddit
        
        Args:
            subreddit: Subreddit name (with r/ prefix)
        """
        subreddit_name = subreddit.replace('r/', '')
        subreddit_config = self.config.get_subreddit_config(subreddit_name)
        
        logger.debug(f"Processing r/{subreddit_name}")
        
        # Check rate limits
        can_comment, reason = self.rate_limiter.can_perform_action(subreddit_name)
        if not can_comment:
            logger.debug(f"Skipping r/{subreddit_name}: {reason}")
            return
        
        # Get recent posts
        max_posts = self.config.get_reddit_config().get('max_posts_per_check', 10)
        posts = self.reddit_client.get_subreddit_posts(
            subreddit_name, 
            sort_method='new', 
            limit=max_posts
        )
        
        if not posts:
            logger.debug(f"No posts found in r/{subreddit_name}")
            return
        
        # Process each post
        for post in posts:
            try:
                if self._should_process_post(post, subreddit_config):
                    self._process_post(post, subreddit_config)
            except Exception as e:
                logger.error(f"Error processing post {post.id}: {e}")
                continue
    
    def _should_process_post(self, post, subreddit_config: Dict[str, Any]) -> bool:
        """Check if a post should be processed"""
        # Check if we've already commented on this post
        recent_comments = self.database.get_recent_comments(hours=24, subreddit=post.subreddit)
        for comment in recent_comments:
            if comment.post_id == post.id:
                logger.debug(f"Already commented on post {post.id}")
                return False
        
        return True
    
    def _process_post(self, post, subreddit_config: Dict[str, Any]):
        """
        Process a single post and potentially comment
        
        Args:
            post: PostData object
            subreddit_config: Subreddit-specific configuration
        """
        logger.debug(f"Analyzing post {post.id} in r/{post.subreddit}")
        
        # Analyze content
        analysis_result = self.content_analyzer.analyze_post(post, subreddit_config)
        
        if not analysis_result.should_comment:
            logger.debug(f"Analysis suggests not commenting on {post.id}: relevance={analysis_result.relevance_score:.2f}")
            return
        
        logger.info(f"Generating comment for post {post.id} (relevance={analysis_result.relevance_score:.2f})")
        
        # Generate comment
        comment_response = self.comment_generator.generate_comment(post, analysis_result, subreddit_config)
        
        if not comment_response:
            logger.warning(f"Failed to generate comment for post {post.id}")
            self._record_failed_action(post, "Comment generation failed")
            return
        
        # Post comment
        dry_run = self.config.is_dry_run()
        success = self.reddit_client.post_comment(post.id, comment_response.comment_text, dry_run)
        
        # Record the action
        comment_record = CommentRecord(
            id=None,
            post_id=post.id,
            subreddit=post.subreddit,
            comment_id=None,  # Will be updated later if successful
            comment_text=comment_response.comment_text,
            posted_at=datetime.now(),
            post_title=post.title,
            post_score=post.score,
            comment_score=None,
            success=success,
            error_message=None if success else "Failed to post comment",
            ai_backend=comment_response.backend_used,
            generation_time=comment_response.generation_time,
            relevance_score=analysis_result.relevance_score,
            sentiment_score=analysis_result.sentiment_score
        )
        
        # Save to database
        self.database.save_comment(comment_record)
        
        # Record action for rate limiting
        self.rate_limiter.record_action(
            subreddit=post.subreddit,
            post_id=post.id,
            action_type='comment',
            success=success,
            error_message=None if success else "Failed to post comment"
        )
        
        if success:
            logger.info(f"Successfully {'simulated' if dry_run else 'posted'} comment on {post.id}")
        else:
            logger.warning(f"Failed to post comment on {post.id}")
    
    def _record_failed_action(self, post, error_message: str):
        """Record a failed action"""
        self.rate_limiter.record_action(
            subreddit=post.subreddit,
            post_id=post.id,
            action_type='comment',
            success=False,
            error_message=error_message
        )
    
    def _cleanup_database(self):
        """Cleanup old database records"""
        try:
            cleanup_days = self.config.get_database_config().get('cleanup_old_records_days', 30)
            self.database.cleanup_old_records(cleanup_days)
            logger.info("Database cleanup completed")
        except Exception as e:
            logger.error(f"Database cleanup failed: {e}")
    
    def _save_rate_limiter_state(self):
        """Save rate limiter state"""
        try:
            # Rate limiter automatically saves state, this is just a periodic backup
            logger.debug("Rate limiter state saved")
        except Exception as e:
            logger.error(f"Failed to save rate limiter state: {e}")
    
    def _log_status(self):
        """Log current bot status"""
        try:
            rate_status = self.rate_limiter.get_status()
            analytics = self.database.get_analytics_data(days=1)
            
            logger.info(f"Bot Status - Comments today: {analytics.successful_comments}, "
                       f"Success rate: {analytics.success_rate:.1%}, "
                       f"Global limits remaining: {rate_status['global_limits']['hour_remaining']}/hour")
        except Exception as e:
            logger.error(f"Failed to log status: {e}")
    
    def _cleanup(self):
        """Cleanup resources before shutdown"""
        logger.info("Cleaning up resources...")
        self.running = False
        
        # Save final state
        try:
            self._save_rate_limiter_state()
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
        
        logger.info("Cleanup completed")


@click.command()
@click.option('--config', default='config', help='Configuration directory path')
@click.option('--dry-run', is_flag=True, help='Run in dry-run mode (no actual posting)')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
def main(config: str, dry_run: bool, verbose: bool):
    """
    RedditSage - AI-Powered Reddit Auto-Comment Bot
    
    An intelligent Reddit bot that automatically finds relevant topics and generates
    helpful, contextual comments while respecting community guidelines.
    """
    try:
        # Override dry run setting if specified
        if dry_run:
            import os
            os.environ['DRY_RUN'] = 'true'
        
        # Initialize and run bot
        bot = RedditBot(config)
        
        if verbose:
            bot.config.update_setting('logging.level', 'DEBUG')
            bot._setup_logging()
        
        bot.run()
        
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Bot crashed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
