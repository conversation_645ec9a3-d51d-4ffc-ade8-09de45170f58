"""
Tests for ContentAnalyzer module
"""

import pytest
from datetime import datetime
import time
from unittest.mock import Mock, patch

from content_analyzer import ContentAnalyzer, AnalysisResult
from reddit_client import PostData


class TestContentAnalyzer:
    """Test cases for ContentAnalyzer"""
    
    @pytest.fixture
    def analyzer_config(self):
        """Test configuration for content analyzer"""
        return {
            'enable_topic_detection': True,
            'min_topic_confidence': 0.6,
            'enable_sentiment_analysis': True,
            'min_relevance_score': 0.7,
            'skip_nsfw_posts': True,
            'min_post_length': 50
        }
    
    @pytest.fixture
    def content_analyzer(self, analyzer_config):
        """Create ContentAnalyzer instance for testing"""
        with patch('content_analyzer.SentenceTransformer'):
            analyzer = ContentAnalyzer(analyzer_config)
            return analyzer
    
    @pytest.fixture
    def sample_post(self):
        """Create sample post data for testing"""
        return PostData(
            id="test123",
            title="How to learn Python programming effectively",
            selftext="I'm a beginner looking for advice on learning Python. What are the best resources and practices?",
            author="testuser",
            subreddit="learnprogramming",
            score=15,
            num_comments=5,
            created_utc=time.time() - 3600,  # 1 hour ago
            url="https://reddit.com/r/learnprogramming/test123",
            is_self=True,
            over_18=False,
            locked=False,
            archived=False,
            removed=False,
            deleted=False
        )
    
    @pytest.fixture
    def subreddit_config(self):
        """Sample subreddit configuration"""
        return {
            'topics_of_interest': ['python', 'programming', 'learning'],
            'avoid_topics': ['politics', 'religion'],
            'min_post_score': 5,
            'min_post_age_minutes': 5,
            'max_post_age_hours': 24,
            'min_post_length': 50,
            'skip_nsfw_posts': True,
            'min_relevance_score': 0.6
        }
    
    def test_analyze_post_success(self, content_analyzer, sample_post, subreddit_config):
        """Test successful post analysis"""
        result = content_analyzer.analyze_post(sample_post, subreddit_config)
        
        assert isinstance(result, AnalysisResult)
        assert result.relevance_score > 0
        assert result.sentiment_label in ['positive', 'negative', 'neutral']
        assert isinstance(result.topics, list)
        assert isinstance(result.keywords, list)
        assert isinstance(result.should_comment, bool)
    
    def test_basic_checks_deleted_post(self, content_analyzer, sample_post, subreddit_config):
        """Test that deleted posts are rejected"""
        sample_post.deleted = True
        result = content_analyzer.analyze_post(sample_post, subreddit_config)
        
        assert not result.should_comment
        assert 'Failed basic quality checks' in result.analysis_metadata.get('rejection_reason', '')
    
    def test_basic_checks_nsfw_post(self, content_analyzer, sample_post, subreddit_config):
        """Test that NSFW posts are rejected when configured"""
        sample_post.over_18 = True
        result = content_analyzer.analyze_post(sample_post, subreddit_config)
        
        assert not result.should_comment
    
    def test_basic_checks_low_score(self, content_analyzer, sample_post, subreddit_config):
        """Test that low-score posts are rejected"""
        sample_post.score = 2  # Below min_post_score of 5
        result = content_analyzer.analyze_post(sample_post, subreddit_config)
        
        assert not result.should_comment
    
    def test_basic_checks_too_old(self, content_analyzer, sample_post, subreddit_config):
        """Test that old posts are rejected"""
        sample_post.created_utc = time.time() - (25 * 3600)  # 25 hours ago
        result = content_analyzer.analyze_post(sample_post, subreddit_config)
        
        assert not result.should_comment
    
    def test_basic_checks_too_new(self, content_analyzer, sample_post, subreddit_config):
        """Test that very new posts are rejected"""
        sample_post.created_utc = time.time() - 60  # 1 minute ago
        result = content_analyzer.analyze_post(sample_post, subreddit_config)
        
        assert not result.should_comment
    
    def test_basic_checks_too_short(self, content_analyzer, sample_post, subreddit_config):
        """Test that short posts are rejected"""
        sample_post.title = "Short"
        sample_post.selftext = "Too short"
        result = content_analyzer.analyze_post(sample_post, subreddit_config)
        
        assert not result.should_comment
    
    def test_sentiment_analysis(self, content_analyzer):
        """Test sentiment analysis functionality"""
        # Positive text
        score, label = content_analyzer._analyze_sentiment("This is amazing and wonderful!")
        assert score > 0
        assert label == "positive"
        
        # Negative text
        score, label = content_analyzer._analyze_sentiment("This is terrible and awful!")
        assert score < 0
        assert label == "negative"
        
        # Neutral text
        score, label = content_analyzer._analyze_sentiment("This is a neutral statement.")
        assert -0.1 <= score <= 0.1
        assert label == "neutral"
    
    def test_topic_detection(self, content_analyzer, subreddit_config):
        """Test topic detection functionality"""
        text = "I'm learning Python programming and need help with machine learning algorithms"
        interests = subreddit_config['topics_of_interest']
        
        topics = content_analyzer._detect_topics(text, interests)
        
        assert 'programming' in topics
        assert 'python' in topics
        assert 'machine_learning' in topics
    
    def test_keyword_extraction(self, content_analyzer):
        """Test keyword extraction"""
        text = "Python programming tutorial for beginners learning data science"
        keywords = content_analyzer._extract_keywords(text)
        
        assert isinstance(keywords, list)
        assert len(keywords) > 0
        assert 'python' in [k.lower() for k in keywords]
        assert 'programming' in [k.lower() for k in keywords]
    
    def test_relevance_scoring(self, content_analyzer, subreddit_config):
        """Test relevance scoring"""
        # High relevance text
        high_relevance_text = "Python programming tutorial for beginners"
        score = content_analyzer._calculate_relevance_score(
            high_relevance_text, ['programming', 'python'], subreddit_config
        )
        assert score > 0.5
        
        # Low relevance text
        low_relevance_text = "Cooking recipes for dinner tonight"
        score = content_analyzer._calculate_relevance_score(
            low_relevance_text, [], subreddit_config
        )
        assert score < 0.3
        
        # Text with avoid topics
        avoid_text = "Political discussion about controversial topics"
        score = content_analyzer._calculate_relevance_score(
            avoid_text, [], subreddit_config
        )
        assert score < 0.2
    
    def test_readability_assessment(self, content_analyzer):
        """Test readability assessment"""
        # Good readability
        good_text = "This is a well-written sentence with moderate length and clear structure."
        score = content_analyzer._assess_readability(good_text)
        assert score > 0.3
        
        # Poor readability (too short)
        poor_text = "Bad."
        score = content_analyzer._assess_readability(poor_text)
        assert score < 0.5
        
        # Empty text
        empty_score = content_analyzer._assess_readability("")
        assert empty_score == 0.0
    
    def test_engagement_potential(self, content_analyzer, sample_post):
        """Test engagement potential calculation"""
        # High engagement post
        sample_post.score = 100
        sample_post.num_comments = 50
        sample_post.created_utc = time.time() - 1800  # 30 minutes ago
        
        score = content_analyzer._calculate_engagement_potential(sample_post)
        assert score > 0.5
        
        # Low engagement post
        sample_post.score = 1
        sample_post.num_comments = 0
        sample_post.created_utc = time.time() - (20 * 3600)  # 20 hours ago
        
        score = content_analyzer._calculate_engagement_potential(sample_post)
        assert score < 0.5
    
    def test_content_quality_assessment(self, content_analyzer, sample_post):
        """Test content quality assessment"""
        score = content_analyzer._assess_content_quality(sample_post, 0.5, 0.7)
        assert 0 <= score <= 1
        
        # Test with negative sentiment
        negative_score = content_analyzer._assess_content_quality(sample_post, -0.8, 0.7)
        assert negative_score < score
    
    def test_comment_decision_logic(self, content_analyzer, subreddit_config):
        """Test comment decision logic"""
        # Should comment - good scores
        should_comment = content_analyzer._should_comment_decision(
            relevance_score=0.8,
            sentiment_score=0.3,
            content_quality=0.7,
            engagement_potential=0.6,
            subreddit_config=subreddit_config
        )
        assert should_comment
        
        # Should not comment - low relevance
        should_not_comment = content_analyzer._should_comment_decision(
            relevance_score=0.3,
            sentiment_score=0.3,
            content_quality=0.7,
            engagement_potential=0.6,
            subreddit_config=subreddit_config
        )
        assert not should_not_comment
        
        # Should not comment - very negative sentiment
        should_not_comment_negative = content_analyzer._should_comment_decision(
            relevance_score=0.8,
            sentiment_score=-0.8,
            content_quality=0.7,
            engagement_potential=0.6,
            subreddit_config=subreddit_config
        )
        assert not should_not_comment_negative
    
    def test_error_handling(self, content_analyzer, subreddit_config):
        """Test error handling in analysis"""
        # Test with malformed post data
        malformed_post = Mock()
        malformed_post.title = None
        malformed_post.selftext = None
        
        # Should not crash and return negative result
        result = content_analyzer.analyze_post(malformed_post, subreddit_config)
        assert isinstance(result, AnalysisResult)
        assert not result.should_comment
