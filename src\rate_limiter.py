"""
Sophisticated Rate Limiting System for Reddit Bot

This module implements advanced rate limiting with per-subreddit limits,
adaptive adjustments, and spam prevention mechanisms.
"""

import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from loguru import logger
import os


@dataclass
class RateLimit:
    """Rate limit configuration"""
    max_per_hour: int
    max_per_day: int
    cooldown_minutes: int
    current_hour_count: int = 0
    current_day_count: int = 0
    last_reset_hour: datetime = None
    last_reset_day: datetime = None
    last_action_time: datetime = None


@dataclass
class ActionRecord:
    """Record of a bot action"""
    timestamp: datetime
    subreddit: str
    post_id: str
    action_type: str  # 'comment', 'vote', etc.
    success: bool
    response_score: Optional[int] = None  # Karma received
    error_message: Optional[str] = None


class RateLimiter:
    """
    Advanced rate limiting system with adaptive features
    """
    
    def __init__(self, config: Dict[str, Any], database_manager=None):
        """
        Initialize rate limiter with configuration
        
        Args:
            config: Configuration dictionary with rate limiting settings
            database_manager: Optional database manager for persistence
        """
        self.config = config
        self.database_manager = database_manager
        
        # Global rate limits
        self.global_limits = RateLimit(
            max_per_hour=config.get('global_comments_per_hour', 5),
            max_per_day=config.get('global_comments_per_day', 50),
            cooldown_minutes=config.get('comment_cooldown_minutes', 10)
        )
        
        # Per-subreddit rate limits
        self.subreddit_limits: Dict[str, RateLimit] = {}
        
        # Action history for analysis
        self.action_history: deque = deque(maxlen=1000)
        
        # Adaptive rate limiting state
        self.adaptive_enabled = config.get('enable_adaptive_limits', True)
        self.karma_threshold = config.get('karma_threshold_adjustment', True)
        self.downvote_penalty = config.get('downvote_penalty_multiplier', 2.0)
        
        # Cooldown tracking
        self.cooldowns: Dict[str, datetime] = {}
        
        # Load persistent state
        self._load_state()
        
        logger.info("Rate limiter initialized with adaptive features")
    
    def can_perform_action(self, subreddit: str, action_type: str = 'comment') -> Tuple[bool, str]:
        """
        Check if an action can be performed given current rate limits
        
        Args:
            subreddit: Target subreddit name
            action_type: Type of action ('comment', 'vote', etc.)
        
        Returns:
            Tuple of (can_perform, reason_if_not)
        """
        try:
            current_time = datetime.now()
            
            # Check global cooldown
            if self._is_in_cooldown('global', current_time):
                remaining = self._get_cooldown_remaining('global', current_time)
                return False, f"Global cooldown active for {remaining:.1f} minutes"
            
            # Check subreddit-specific cooldown
            subreddit_key = f"subreddit_{subreddit}"
            if self._is_in_cooldown(subreddit_key, current_time):
                remaining = self._get_cooldown_remaining(subreddit_key, current_time)
                return False, f"Subreddit cooldown active for {remaining:.1f} minutes"
            
            # Update rate limit counters
            self._update_rate_limits(current_time)
            
            # Check global limits
            if not self._check_global_limits():
                return False, "Global rate limits exceeded"
            
            # Check subreddit limits
            if not self._check_subreddit_limits(subreddit):
                return False, f"Subreddit rate limits exceeded for r/{subreddit}"
            
            # Check adaptive limits if enabled
            if self.adaptive_enabled:
                adaptive_check, reason = self._check_adaptive_limits(subreddit)
                if not adaptive_check:
                    return False, f"Adaptive limit: {reason}"
            
            return True, "Action allowed"
            
        except Exception as e:
            logger.error(f"Error checking rate limits: {e}")
            return False, f"Rate limit check error: {e}"
    
    def record_action(self, subreddit: str, post_id: str, action_type: str, 
                     success: bool, response_score: Optional[int] = None,
                     error_message: Optional[str] = None):
        """
        Record an action for rate limiting and analysis
        
        Args:
            subreddit: Subreddit where action was performed
            post_id: Reddit post ID
            action_type: Type of action performed
            success: Whether the action was successful
            response_score: Karma score received (if applicable)
            error_message: Error message if action failed
        """
        try:
            current_time = datetime.now()
            
            # Create action record
            record = ActionRecord(
                timestamp=current_time,
                subreddit=subreddit,
                post_id=post_id,
                action_type=action_type,
                success=success,
                response_score=response_score,
                error_message=error_message
            )
            
            # Add to history
            self.action_history.append(record)
            
            if success:
                # Update counters
                self.global_limits.current_hour_count += 1
                self.global_limits.current_day_count += 1
                self.global_limits.last_action_time = current_time
                
                # Update subreddit counters
                subreddit_limit = self._get_subreddit_limit(subreddit)
                subreddit_limit.current_hour_count += 1
                subreddit_limit.current_day_count += 1
                subreddit_limit.last_action_time = current_time
                
                # Set cooldowns
                self._set_cooldown('global', current_time, self.global_limits.cooldown_minutes)
                self._set_cooldown(f"subreddit_{subreddit}", current_time, subreddit_limit.cooldown_minutes)
                
                logger.info(f"Recorded successful {action_type} in r/{subreddit}")
            else:
                # Handle failed actions
                self._handle_failed_action(subreddit, error_message, current_time)
            
            # Adaptive adjustments
            if self.adaptive_enabled:
                self._apply_adaptive_adjustments(subreddit, record)
            
            # Save state
            self._save_state()
            
        except Exception as e:
            logger.error(f"Error recording action: {e}")
    
    def _update_rate_limits(self, current_time: datetime):
        """Update rate limit counters based on time windows"""
        # Reset hourly counters
        if (self.global_limits.last_reset_hour is None or 
            current_time - self.global_limits.last_reset_hour >= timedelta(hours=1)):
            self.global_limits.current_hour_count = 0
            self.global_limits.last_reset_hour = current_time
        
        # Reset daily counters
        if (self.global_limits.last_reset_day is None or 
            current_time - self.global_limits.last_reset_day >= timedelta(days=1)):
            self.global_limits.current_day_count = 0
            self.global_limits.last_reset_day = current_time
        
        # Update subreddit limits
        for subreddit_limit in self.subreddit_limits.values():
            if (subreddit_limit.last_reset_hour is None or 
                current_time - subreddit_limit.last_reset_hour >= timedelta(hours=1)):
                subreddit_limit.current_hour_count = 0
                subreddit_limit.last_reset_hour = current_time
            
            if (subreddit_limit.last_reset_day is None or 
                current_time - subreddit_limit.last_reset_day >= timedelta(days=1)):
                subreddit_limit.current_day_count = 0
                subreddit_limit.last_reset_day = current_time
    
    def _check_global_limits(self) -> bool:
        """Check if global rate limits are exceeded"""
        return (self.global_limits.current_hour_count < self.global_limits.max_per_hour and
                self.global_limits.current_day_count < self.global_limits.max_per_day)
    
    def _check_subreddit_limits(self, subreddit: str) -> bool:
        """Check if subreddit-specific rate limits are exceeded"""
        subreddit_limit = self._get_subreddit_limit(subreddit)
        return (subreddit_limit.current_hour_count < subreddit_limit.max_per_hour and
                subreddit_limit.current_day_count < subreddit_limit.max_per_day)
    
    def _get_subreddit_limit(self, subreddit: str) -> RateLimit:
        """Get or create rate limit for subreddit"""
        if subreddit not in self.subreddit_limits:
            self.subreddit_limits[subreddit] = RateLimit(
                max_per_hour=self.config.get('subreddit_comments_per_hour', 2),
                max_per_day=self.config.get('subreddit_comments_per_day', 10),
                cooldown_minutes=self.config.get('comment_cooldown_minutes', 10)
            )
        return self.subreddit_limits[subreddit]
    
    def _is_in_cooldown(self, key: str, current_time: datetime) -> bool:
        """Check if a cooldown is active"""
        if key not in self.cooldowns:
            return False
        return current_time < self.cooldowns[key]
    
    def _get_cooldown_remaining(self, key: str, current_time: datetime) -> float:
        """Get remaining cooldown time in minutes"""
        if key not in self.cooldowns:
            return 0.0
        remaining = (self.cooldowns[key] - current_time).total_seconds() / 60
        return max(0.0, remaining)
    
    def _set_cooldown(self, key: str, current_time: datetime, minutes: int):
        """Set a cooldown period"""
        self.cooldowns[key] = current_time + timedelta(minutes=minutes)
    
    def _check_adaptive_limits(self, subreddit: str) -> Tuple[bool, str]:
        """Check adaptive rate limits based on performance"""
        try:
            # Get recent performance for this subreddit
            recent_actions = [r for r in self.action_history 
                            if r.subreddit == subreddit and 
                            r.timestamp > datetime.now() - timedelta(hours=24)]
            
            if len(recent_actions) < 3:  # Not enough data
                return True, "Insufficient data for adaptive limits"
            
            # Calculate success rate
            successful_actions = [r for r in recent_actions if r.success]
            success_rate = len(successful_actions) / len(recent_actions)
            
            # Calculate average karma (if available)
            karma_scores = [r.response_score for r in successful_actions 
                          if r.response_score is not None]
            avg_karma = sum(karma_scores) / len(karma_scores) if karma_scores else 0
            
            # Apply adaptive rules
            if success_rate < 0.7:  # Low success rate
                return False, f"Low success rate ({success_rate:.1%})"
            
            if self.karma_threshold and avg_karma < -2:  # Negative karma
                return False, f"Poor karma performance (avg: {avg_karma:.1f})"
            
            # Check for recent errors
            recent_errors = [r for r in recent_actions 
                           if not r.success and 
                           r.timestamp > datetime.now() - timedelta(hours=1)]
            
            if len(recent_errors) >= 2:  # Multiple recent errors
                return False, "Multiple recent errors detected"
            
            return True, "Adaptive limits passed"
            
        except Exception as e:
            logger.warning(f"Error in adaptive limit check: {e}")
            return True, "Adaptive check error - allowing action"

    def _handle_failed_action(self, subreddit: str, error_message: Optional[str], current_time: datetime):
        """Handle failed actions with appropriate penalties"""
        try:
            # Increase cooldown for certain types of errors
            if error_message:
                error_lower = error_message.lower()

                if 'rate limit' in error_lower or 'too many requests' in error_lower:
                    # Severe rate limiting penalty
                    penalty_minutes = self.config.get('rate_limit_cooldown_minutes', 60)
                    self._set_cooldown('global', current_time, penalty_minutes)
                    logger.warning(f"Applied {penalty_minutes}min cooldown due to rate limiting")

                elif 'forbidden' in error_lower or 'banned' in error_lower:
                    # Subreddit-specific penalty
                    penalty_minutes = self.config.get('error_cooldown_minutes', 30)
                    self._set_cooldown(f"subreddit_{subreddit}", current_time, penalty_minutes)
                    logger.warning(f"Applied {penalty_minutes}min cooldown for r/{subreddit} due to access error")

                else:
                    # General error penalty
                    penalty_minutes = self.config.get('error_cooldown_minutes', 30) // 2
                    self._set_cooldown('global', current_time, penalty_minutes)
                    logger.info(f"Applied {penalty_minutes}min cooldown due to general error")

        except Exception as e:
            logger.error(f"Error handling failed action: {e}")

    def _apply_adaptive_adjustments(self, subreddit: str, record: ActionRecord):
        """Apply adaptive adjustments based on action results"""
        try:
            if not record.success:
                return

            # Adjust limits based on karma performance
            if record.response_score is not None and self.karma_threshold:
                subreddit_limit = self._get_subreddit_limit(subreddit)

                if record.response_score < -2:  # Heavily downvoted
                    # Reduce limits temporarily
                    subreddit_limit.max_per_hour = max(1, int(subreddit_limit.max_per_hour * 0.8))
                    subreddit_limit.max_per_day = max(2, int(subreddit_limit.max_per_day * 0.9))
                    logger.info(f"Reduced limits for r/{subreddit} due to negative karma")

                elif record.response_score > 5:  # Well received
                    # Gradually increase limits (but not beyond config maximums)
                    max_hour = self.config.get('subreddit_comments_per_hour', 2)
                    max_day = self.config.get('subreddit_comments_per_day', 10)

                    subreddit_limit.max_per_hour = min(max_hour, subreddit_limit.max_per_hour + 1)
                    subreddit_limit.max_per_day = min(max_day, subreddit_limit.max_per_day + 1)
                    logger.info(f"Increased limits for r/{subreddit} due to positive karma")

        except Exception as e:
            logger.error(f"Error applying adaptive adjustments: {e}")

    def _save_state(self):
        """Save rate limiter state to persistent storage"""
        try:
            if self.database_manager:
                # Save to database
                state_data = {
                    'global_limits': asdict(self.global_limits),
                    'subreddit_limits': {k: asdict(v) for k, v in self.subreddit_limits.items()},
                    'cooldowns': {k: v.isoformat() for k, v in self.cooldowns.items()},
                    'last_updated': datetime.now().isoformat()
                }
                self.database_manager.save_rate_limiter_state(state_data)
            else:
                # Save to file as fallback
                state_file = 'data/rate_limiter_state.json'
                os.makedirs(os.path.dirname(state_file), exist_ok=True)

                # Convert datetime objects to strings for JSON serialization
                state_data = {
                    'global_limits': self._serialize_rate_limit(self.global_limits),
                    'subreddit_limits': {k: self._serialize_rate_limit(v) for k, v in self.subreddit_limits.items()},
                    'cooldowns': {k: v.isoformat() for k, v in self.cooldowns.items()},
                    'last_updated': datetime.now().isoformat()
                }

                with open(state_file, 'w') as f:
                    json.dump(state_data, f, indent=2)

        except Exception as e:
            logger.error(f"Error saving rate limiter state: {e}")

    def _load_state(self):
        """Load rate limiter state from persistent storage"""
        try:
            if self.database_manager:
                # Load from database
                state_data = self.database_manager.load_rate_limiter_state()
                if state_data:
                    self._deserialize_state(state_data)
            else:
                # Load from file as fallback
                state_file = 'data/rate_limiter_state.json'
                if os.path.exists(state_file):
                    with open(state_file, 'r') as f:
                        state_data = json.load(f)
                    self._deserialize_state(state_data)

        except Exception as e:
            logger.warning(f"Could not load rate limiter state: {e}")

    def _serialize_rate_limit(self, rate_limit: RateLimit) -> Dict[str, Any]:
        """Serialize RateLimit object for JSON storage"""
        data = asdict(rate_limit)
        # Convert datetime objects to ISO strings
        for key in ['last_reset_hour', 'last_reset_day', 'last_action_time']:
            if data[key] is not None:
                data[key] = data[key].isoformat()
        return data

    def _deserialize_state(self, state_data: Dict[str, Any]):
        """Deserialize state data and restore rate limiter state"""
        try:
            # Restore global limits
            if 'global_limits' in state_data:
                global_data = state_data['global_limits']
                self.global_limits = self._deserialize_rate_limit(global_data)

            # Restore subreddit limits
            if 'subreddit_limits' in state_data:
                for subreddit, limit_data in state_data['subreddit_limits'].items():
                    self.subreddit_limits[subreddit] = self._deserialize_rate_limit(limit_data)

            # Restore cooldowns
            if 'cooldowns' in state_data:
                for key, iso_time in state_data['cooldowns'].items():
                    self.cooldowns[key] = datetime.fromisoformat(iso_time)

            logger.info("Successfully loaded rate limiter state")

        except Exception as e:
            logger.error(f"Error deserializing state: {e}")

    def _deserialize_rate_limit(self, data: Dict[str, Any]) -> RateLimit:
        """Deserialize RateLimit object from stored data"""
        # Convert ISO strings back to datetime objects
        for key in ['last_reset_hour', 'last_reset_day', 'last_action_time']:
            if data.get(key):
                data[key] = datetime.fromisoformat(data[key])
            else:
                data[key] = None

        return RateLimit(**data)

    def get_status(self) -> Dict[str, Any]:
        """Get current rate limiter status"""
        current_time = datetime.now()
        self._update_rate_limits(current_time)

        return {
            'global_limits': {
                'hour_remaining': self.global_limits.max_per_hour - self.global_limits.current_hour_count,
                'day_remaining': self.global_limits.max_per_day - self.global_limits.current_day_count,
                'cooldown_remaining': self._get_cooldown_remaining('global', current_time)
            },
            'subreddit_limits': {
                subreddit: {
                    'hour_remaining': limit.max_per_hour - limit.current_hour_count,
                    'day_remaining': limit.max_per_day - limit.current_day_count,
                    'cooldown_remaining': self._get_cooldown_remaining(f"subreddit_{subreddit}", current_time)
                }
                for subreddit, limit in self.subreddit_limits.items()
            },
            'recent_actions': len([r for r in self.action_history
                                 if r.timestamp > current_time - timedelta(hours=1)]),
            'success_rate_24h': self._calculate_success_rate(24),
            'adaptive_enabled': self.adaptive_enabled
        }

    def _calculate_success_rate(self, hours: int) -> float:
        """Calculate success rate over the specified time period"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_actions = [r for r in self.action_history if r.timestamp > cutoff_time]

        if not recent_actions:
            return 1.0

        successful = sum(1 for r in recent_actions if r.success)
        return successful / len(recent_actions)

    def reset_limits(self, subreddit: Optional[str] = None):
        """Reset rate limits (for testing or emergency situations)"""
        if subreddit:
            if subreddit in self.subreddit_limits:
                limit = self.subreddit_limits[subreddit]
                limit.current_hour_count = 0
                limit.current_day_count = 0
                self.cooldowns.pop(f"subreddit_{subreddit}", None)
                logger.info(f"Reset rate limits for r/{subreddit}")
        else:
            self.global_limits.current_hour_count = 0
            self.global_limits.current_day_count = 0
            self.cooldowns.clear()
            for limit in self.subreddit_limits.values():
                limit.current_hour_count = 0
                limit.current_day_count = 0
            logger.info("Reset all rate limits")

        self._save_state()
