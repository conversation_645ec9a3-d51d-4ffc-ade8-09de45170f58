"""
Test suite for RedditSage - AI-Powered Reddit Auto-Comment Bot
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Add src to path for testing
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Test configuration directory
TEST_CONFIG_DIR = os.path.join(os.path.dirname(__file__), 'test_config')

def create_test_config():
    """Create test configuration files"""
    os.makedirs(TEST_CONFIG_DIR, exist_ok=True)
    
    # Create test settings.yaml
    settings_content = """
bot:
  name: "TestBot"
  version: "1.0.0"

reddit:
  requests_per_minute: 10
  max_comments_per_hour: 2
  max_comments_per_day: 10

ai:
  primary_backend: "rule_based"
  fallback_backends: ["rule_based"]
  temperature: 0.7

rate_limiting:
  global_comments_per_hour: 2
  global_comments_per_day: 10
  enable_adaptive_limits: false

safety:
  dry_run: true
  enable_content_moderation: true
"""
    
    with open(os.path.join(TEST_CONFIG_DIR, 'settings.yaml'), 'w') as f:
        f.write(settings_content)
    
    # Create test subreddits.yaml
    subreddits_content = """
default:
  enabled: true
  max_comments_per_day: 1
  comment_style: "helpful"

technology:
  r/test:
    enabled: true
    max_comments_per_day: 2
    comment_style: "technical"

comment_styles:
  helpful:
    tone: "friendly and supportive"
    length: "medium"
  technical:
    tone: "professional and precise"
    length: "detailed"
"""
    
    with open(os.path.join(TEST_CONFIG_DIR, 'subreddits.yaml'), 'w') as f:
        f.write(subreddits_content)
    
    # Create test prompts.yaml
    prompts_content = """
system_prompts:
  helpful:
    prompt: "You are a helpful test assistant."

comment_templates:
  generic:
    - "This is a test comment."
    - "Another test comment."

fallback_prompts:
  generic_helpful:
    prompt: "Generate a helpful test comment."
"""
    
    with open(os.path.join(TEST_CONFIG_DIR, 'prompts.yaml'), 'w') as f:
        f.write(prompts_content)

def cleanup_test_config():
    """Clean up test configuration files"""
    if os.path.exists(TEST_CONFIG_DIR):
        shutil.rmtree(TEST_CONFIG_DIR)

def create_temp_database():
    """Create a temporary database for testing"""
    temp_dir = tempfile.mkdtemp()
    return os.path.join(temp_dir, 'test_comments.db')

def cleanup_temp_database(db_path):
    """Clean up temporary database"""
    if os.path.exists(db_path):
        os.remove(db_path)
    
    # Remove temp directory if empty
    temp_dir = os.path.dirname(db_path)
    try:
        os.rmdir(temp_dir)
    except OSError:
        pass  # Directory not empty or doesn't exist
