# Contributing to RedditSage

Thank you for your interest in contributing to RedditSage! This document provides guidelines and information for contributors.

## 🤝 How to Contribute

### Reporting Issues
- Use the [GitHub Issues](https://github.com/HectorTa1989/reddit-auto-comment/issues) page
- Search existing issues before creating a new one
- Provide detailed information including:
  - Steps to reproduce
  - Expected vs actual behavior
  - Environment details (OS, Python version, etc.)
  - Log files or error messages

### Suggesting Features
- Open a [Feature Request](https://github.com/HectorTa1989/reddit-auto-comment/issues/new?template=feature_request.md)
- Describe the feature and its benefits
- Include use cases and examples
- Consider implementation complexity

### Code Contributions

#### Getting Started
1. Fork the repository
2. Clone your fork: `git clone https://github.com/yourusername/reddit-auto-comment.git`
3. Create a virtual environment: `python -m venv venv`
4. Activate it: `source venv/bin/activate` (Linux/Mac) or `venv\Scripts\activate` (Windows)
5. Install dependencies: `pip install -r requirements.txt`
6. Install development dependencies: `pip install -e .[dev]`

#### Development Workflow
1. Create a feature branch: `git checkout -b feature/your-feature-name`
2. Make your changes
3. Write or update tests
4. Run the test suite: `pytest`
5. Check code style: `black src/ tests/` and `flake8 src/ tests/`
6. Update documentation if needed
7. Commit your changes with descriptive messages
8. Push to your fork: `git push origin feature/your-feature-name`
9. Create a Pull Request

## 📋 Development Guidelines

### Code Style
- Follow PEP 8 Python style guidelines
- Use Black for code formatting: `black src/ tests/`
- Use type hints where appropriate
- Write descriptive docstrings for all public functions and classes
- Keep line length under 100 characters

### Testing
- Write unit tests for all new functionality
- Maintain test coverage above 80%
- Use pytest for testing framework
- Mock external dependencies (Reddit API, AI APIs)
- Test both success and failure scenarios

### Documentation
- Update README.md for user-facing changes
- Add docstrings to all public functions and classes
- Update configuration documentation for new settings
- Include examples in docstrings where helpful

### Commit Messages
Follow the conventional commit format:
```
type(scope): description

[optional body]

[optional footer]
```

Types:
- `feat`: New features
- `fix`: Bug fixes
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Test additions or changes
- `chore`: Maintenance tasks

Examples:
- `feat(ai): add support for Claude API backend`
- `fix(rate-limiter): resolve cooldown calculation bug`
- `docs(readme): update installation instructions`

## 🏗️ Architecture Guidelines

### Adding New AI Backends
1. Create a new class inheriting from `AIBackend`
2. Implement required methods: `generate_comment()` and `is_available()`
3. Add configuration options to `config/settings.yaml`
4. Update the `CommentGenerator` to include your backend
5. Write comprehensive tests
6. Update documentation

### Adding New Features
1. Follow the existing modular architecture
2. Add configuration options to appropriate YAML files
3. Implement comprehensive error handling
4. Add logging for debugging and monitoring
5. Write unit tests with good coverage
6. Update documentation and examples

### Database Changes
1. Create migration scripts for schema changes
2. Maintain backward compatibility when possible
3. Update the `DatabaseManager` class
4. Test with existing data
5. Document any breaking changes

## 🧪 Testing Guidelines

### Running Tests
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src

# Run specific test file
pytest tests/test_content_analyzer.py

# Run with verbose output
pytest -v
```

### Writing Tests
- Use descriptive test names that explain what is being tested
- Follow the Arrange-Act-Assert pattern
- Use fixtures for common test setup
- Mock external dependencies
- Test edge cases and error conditions

### Test Structure
```python
def test_feature_description():
    # Arrange
    setup_test_data()
    
    # Act
    result = function_under_test()
    
    # Assert
    assert result == expected_value
```

## 📦 Release Process

### Version Numbering
We follow [Semantic Versioning](https://semver.org/):
- MAJOR: Breaking changes
- MINOR: New features (backward compatible)
- PATCH: Bug fixes (backward compatible)

### Release Checklist
1. Update version in `setup.py` and `src/__init__.py`
2. Update `CHANGELOG.md` with new features and fixes
3. Run full test suite
4. Update documentation
5. Create release branch
6. Tag the release: `git tag v1.0.0`
7. Push tags: `git push origin --tags`
8. Create GitHub release with changelog

## 🔒 Security

### Reporting Security Issues
- **DO NOT** open public issues for security vulnerabilities
- Email security issues to: [<EMAIL>]
- Include detailed information about the vulnerability
- Allow time for investigation and fix before public disclosure

### Security Guidelines
- Never commit API keys or credentials
- Use environment variables for sensitive configuration
- Validate all user inputs
- Follow secure coding practices
- Keep dependencies updated

## 📞 Getting Help

### Community Support
- [GitHub Discussions](https://github.com/HectorTa1989/reddit-auto-comment/discussions)
- [Discord Server](https://discord.gg/your-server) (if available)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/reddit-sage)

### Documentation
- [Wiki](https://github.com/HectorTa1989/reddit-auto-comment/wiki)
- [API Documentation](https://reddit-sage.readthedocs.io)
- [Configuration Guide](https://github.com/HectorTa1989/reddit-auto-comment/wiki/Configuration)

## 📄 License

By contributing to RedditSage, you agree that your contributions will be licensed under the MIT License.

## 🙏 Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes
- Project documentation

Thank you for helping make RedditSage better! 🚀
