"""
Configuration Manager for Reddit Bot

This module handles loading and managing configuration from YAML files,
environment variables, and database settings.
"""

import os
import yaml
from typing import Dict, Any, Optional, List
from pathlib import Path
from loguru import logger
from dotenv import load_dotenv


class Config:
    """
    Comprehensive configuration manager for the Reddit bot
    """
    
    def __init__(self, config_dir: str = "config"):
        """
        Initialize configuration manager
        
        Args:
            config_dir: Directory containing configuration files
        """
        self.config_dir = Path(config_dir)
        self.settings = {}
        self.subreddits = {}
        self.prompts = {}
        
        # Load environment variables
        load_dotenv()
        
        # Load all configuration files
        self._load_configurations()
        
        # Validate critical settings
        self._validate_configuration()
        
        logger.info("Configuration loaded successfully")
    
    def _load_configurations(self):
        """Load all configuration files"""
        try:
            # Load main settings
            settings_file = self.config_dir / "settings.yaml"
            if settings_file.exists():
                with open(settings_file, 'r') as f:
                    self.settings = yaml.safe_load(f) or {}
                logger.info("Loaded main settings configuration")
            else:
                logger.warning(f"Settings file not found: {settings_file}")
                self.settings = self._get_default_settings()
            
            # Load subreddit configuration
            subreddits_file = self.config_dir / "subreddits.yaml"
            if subreddits_file.exists():
                with open(subreddits_file, 'r') as f:
                    self.subreddits = yaml.safe_load(f) or {}
                logger.info("Loaded subreddit configuration")
            else:
                logger.warning(f"Subreddits file not found: {subreddits_file}")
                self.subreddits = self._get_default_subreddits()
            
            # Load prompts configuration
            prompts_file = self.config_dir / "prompts.yaml"
            if prompts_file.exists():
                with open(prompts_file, 'r') as f:
                    self.prompts = yaml.safe_load(f) or {}
                logger.info("Loaded prompts configuration")
            else:
                logger.warning(f"Prompts file not found: {prompts_file}")
                self.prompts = self._get_default_prompts()
        
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            raise
    
    def _validate_configuration(self):
        """Validate critical configuration settings"""
        required_env_vars = [
            'REDDIT_CLIENT_ID',
            'REDDIT_CLIENT_SECRET',
            'REDDIT_USER_AGENT',
            'REDDIT_USERNAME',
            'REDDIT_PASSWORD'
        ]
        
        missing_vars = []
        for var in required_env_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            logger.error(f"Missing required environment variables: {missing_vars}")
            raise ValueError(f"Missing required environment variables: {missing_vars}")
        
        # Validate AI backend configuration
        ai_config = self.get_ai_config()
        primary_backend = ai_config.get('primary_backend', 'groq')
        
        if primary_backend == 'groq' and not os.getenv('GROQ_API_KEY'):
            logger.warning("Groq API key not found, will fall back to other backends")
        
        if primary_backend == 'openai' and not os.getenv('OPENAI_API_KEY'):
            logger.warning("OpenAI API key not found, will fall back to other backends")
        
        logger.info("Configuration validation completed")
    
    def get_reddit_config(self) -> Dict[str, Any]:
        """Get Reddit API configuration"""
        return self.settings.get('reddit', {})
    
    def get_ai_config(self) -> Dict[str, Any]:
        """Get AI configuration"""
        return self.settings.get('ai', {})
    
    def get_rate_limiting_config(self) -> Dict[str, Any]:
        """Get rate limiting configuration"""
        return self.settings.get('rate_limiting', {})
    
    def get_content_analysis_config(self) -> Dict[str, Any]:
        """Get content analysis configuration"""
        return self.settings.get('content_analysis', {})
    
    def get_database_config(self) -> Dict[str, Any]:
        """Get database configuration"""
        db_config = self.settings.get('database', {})
        
        # Override with environment variable if set
        if os.getenv('DATABASE_PATH'):
            db_config['path'] = os.getenv('DATABASE_PATH')
        
        return db_config
    
    def get_logging_config(self) -> Dict[str, Any]:
        """Get logging configuration"""
        logging_config = self.settings.get('logging', {})
        
        # Override with environment variables if set
        if os.getenv('LOG_LEVEL'):
            logging_config['level'] = os.getenv('LOG_LEVEL')
        
        if os.getenv('LOG_FILE'):
            logging_config['file'] = os.getenv('LOG_FILE')
        
        return logging_config
    
    def get_monitoring_config(self) -> Dict[str, Any]:
        """Get monitoring configuration"""
        return self.settings.get('monitoring', {})
    
    def get_safety_config(self) -> Dict[str, Any]:
        """Get safety and compliance configuration"""
        safety_config = self.settings.get('safety', {})
        
        # Override dry run with environment variable
        if os.getenv('DRY_RUN'):
            safety_config['dry_run'] = os.getenv('DRY_RUN').lower() == 'true'
        
        return safety_config
    
    def get_features_config(self) -> Dict[str, Any]:
        """Get features configuration"""
        return self.settings.get('features', {})
    
    def get_subreddit_config(self, subreddit: str) -> Dict[str, Any]:
        """
        Get configuration for a specific subreddit
        
        Args:
            subreddit: Subreddit name (with or without r/ prefix)
        
        Returns:
            Subreddit-specific configuration merged with defaults
        """
        # Normalize subreddit name
        subreddit_name = subreddit.replace('r/', '') if subreddit.startswith('r/') else subreddit
        
        # Get default configuration
        default_config = self.subreddits.get('default', {})
        
        # Look for subreddit-specific config in different categories
        subreddit_config = {}
        
        # Check direct subreddit name
        full_subreddit_name = f"r/{subreddit_name}"
        if full_subreddit_name in self.subreddits:
            subreddit_config = self.subreddits[full_subreddit_name]
        else:
            # Check in categories
            for category, subreddits in self.subreddits.items():
                if category == 'default' or category == 'comment_styles':
                    continue
                
                if isinstance(subreddits, dict) and full_subreddit_name in subreddits:
                    subreddit_config = subreddits[full_subreddit_name]
                    break
        
        # Merge default with specific configuration
        merged_config = {**default_config, **subreddit_config}
        
        return merged_config
    
    def get_comment_style_config(self, style: str) -> Dict[str, Any]:
        """
        Get configuration for a specific comment style
        
        Args:
            style: Comment style name
        
        Returns:
            Comment style configuration
        """
        comment_styles = self.subreddits.get('comment_styles', {})
        return comment_styles.get(style, {})
    
    def get_prompt_config(self, prompt_type: str) -> Dict[str, Any]:
        """
        Get prompt configuration for a specific type
        
        Args:
            prompt_type: Type of prompt (system_prompts, comment_templates, etc.)
        
        Returns:
            Prompt configuration
        """
        return self.prompts.get(prompt_type, {})
    
    def get_enabled_subreddits(self) -> List[str]:
        """Get list of enabled subreddits"""
        enabled = []
        
        for category, subreddits in self.subreddits.items():
            if category in ['default', 'comment_styles']:
                continue
            
            if isinstance(subreddits, dict):
                for subreddit, config in subreddits.items():
                    if config.get('enabled', True):
                        enabled.append(subreddit)
        
        return enabled
    
    def is_dry_run(self) -> bool:
        """Check if bot is in dry run mode"""
        return self.get_safety_config().get('dry_run', False)
    
    def get_max_comments_per_hour(self) -> int:
        """Get global maximum comments per hour"""
        return self.get_rate_limiting_config().get('global_comments_per_hour', 5)
    
    def get_max_comments_per_day(self) -> int:
        """Get global maximum comments per day"""
        return self.get_rate_limiting_config().get('global_comments_per_day', 50)
    
    def _get_default_settings(self) -> Dict[str, Any]:
        """Get default settings if configuration file is missing"""
        return {
            'bot': {
                'name': 'RedditSage',
                'version': '1.0.0'
            },
            'reddit': {
                'requests_per_minute': 30,
                'max_comments_per_hour': 5,
                'max_comments_per_day': 50,
                'min_comment_interval_minutes': 10
            },
            'ai': {
                'primary_backend': 'groq',
                'fallback_backends': ['local', 'rule_based'],
                'temperature': 0.7,
                'max_tokens': 300
            },
            'rate_limiting': {
                'global_comments_per_hour': 5,
                'global_comments_per_day': 50,
                'enable_adaptive_limits': True
            },
            'safety': {
                'dry_run': True,
                'enable_content_moderation': True
            }
        }
    
    def _get_default_subreddits(self) -> Dict[str, Any]:
        """Get default subreddit configuration"""
        return {
            'default': {
                'enabled': True,
                'max_comments_per_day': 3,
                'comment_style': 'helpful'
            }
        }
    
    def _get_default_prompts(self) -> Dict[str, Any]:
        """Get default prompts configuration"""
        return {
            'system_prompts': {
                'helpful': {
                    'prompt': 'You are a helpful Reddit community member who provides valuable, constructive comments.'
                }
            },
            'comment_templates': {
                'generic': [
                    'Thanks for sharing this! Very informative.',
                    'Great post! This is exactly what the community needs.',
                    'Appreciate you taking the time to write this up.'
                ]
            }
        }
    
    def reload_configuration(self):
        """Reload configuration from files"""
        logger.info("Reloading configuration...")
        self._load_configurations()
        self._validate_configuration()
        logger.info("Configuration reloaded successfully")
    
    def update_setting(self, key_path: str, value: Any):
        """
        Update a setting value dynamically
        
        Args:
            key_path: Dot-separated path to the setting (e.g., 'ai.temperature')
            value: New value for the setting
        """
        keys = key_path.split('.')
        current = self.settings
        
        # Navigate to the parent of the target key
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        # Set the value
        current[keys[-1]] = value
        logger.info(f"Updated setting {key_path} = {value}")
    
    def get_setting(self, key_path: str, default: Any = None) -> Any:
        """
        Get a setting value by dot-separated path
        
        Args:
            key_path: Dot-separated path to the setting
            default: Default value if setting not found
        
        Returns:
            Setting value or default
        """
        keys = key_path.split('.')
        current = self.settings
        
        try:
            for key in keys:
                current = current[key]
            return current
        except (KeyError, TypeError):
            return default
