# RedditSage - AI-Powered Reddit Auto-Comment Bot
# Multi-stage Docker build for production deployment

# Build stage
FROM python:3.11-slim as builder

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create and activate virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --upgrade pip && \
    pip install -r requirements.txt

# Production stage
FROM python:3.11-slim as production

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PATH="/opt/venv/bin:$PATH"

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user for security
RUN groupadd -r reddit && useradd -r -g reddit reddit

# Copy virtual environment from builder stage
COPY --from=builder /opt/venv /opt/venv

# Set working directory
WORKDIR /app

# Copy application code
COPY src/ ./src/
COPY config/ ./config/
COPY setup.py ./
COPY README.md ./
COPY LICENSE ./

# Create data directory with proper permissions
RUN mkdir -p /app/data/logs && \
    chown -R reddit:reddit /app

# Switch to non-root user
USER reddit

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python -c "import sys; sys.path.append('/app'); from src.main import RedditBot; bot = RedditBot(); exit(0 if bot._health_check() else 1)" || exit 1

# Expose port for monitoring (if web interface is enabled)
EXPOSE 8080

# Default command
CMD ["python", "-m", "src.main"]

# Labels for metadata
LABEL maintainer="HectorTa1989 <<EMAIL>>" \
      version="1.0.0" \
      description="RedditSage - AI-Powered Reddit Auto-Comment Bot" \
      org.opencontainers.image.title="RedditSage" \
      org.opencontainers.image.description="AI-Powered Reddit Auto-Comment Bot" \
      org.opencontainers.image.version="1.0.0" \
      org.opencontainers.image.vendor="HectorTa1989" \
      org.opencontainers.image.licenses="MIT" \
      org.opencontainers.image.source="https://github.com/HectorTa1989/reddit-auto-comment"
