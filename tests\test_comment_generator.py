"""
Tests for CommentGenerator module
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
import time

from comment_generator import (
    CommentGenerator, CommentRequest, CommentResponse,
    GroqBackend, OpenAIBackend, LocalTransformerBackend, RuleBasedBackend
)


class TestCommentGenerator:
    """Test cases for CommentGenerator"""
    
    @pytest.fixture
    def generator_config(self):
        """Test configuration for comment generator"""
        return {
            'primary_backend': 'rule_based',
            'fallback_backends': ['rule_based'],
            'temperature': 0.7,
            'max_comment_length': 500,
            'min_comment_length': 50,
            'enable_local_ai': False
        }
    
    @pytest.fixture
    def prompts_config(self):
        """Test prompts configuration"""
        return {
            'system_prompts': {
                'helpful': {
                    'prompt': 'You are a helpful assistant.'
                }
            },
            'comment_templates': {
                'generic': ['Test comment template.']
            }
        }
    
    @pytest.fixture
    def comment_generator(self, generator_config, prompts_config):
        """Create CommentGenerator instance for testing"""
        with patch.dict('os.environ', {}, clear=True):
            generator = CommentGenerator(generator_config, prompts_config)
            return generator
    
    @pytest.fixture
    def sample_request(self):
        """Create sample comment request"""
        return CommentRequest(
            post_title="How to learn Python?",
            post_content="I'm a beginner looking for Python learning resources.",
            subreddit="learnprogramming",
            comment_style="helpful",
            topics=["python", "learning"],
            context={"post_score": 10, "sentiment": "positive"},
            max_length=300,
            temperature=0.7
        )
    
    @pytest.fixture
    def mock_post_data(self):
        """Mock post data"""
        mock_post = Mock()
        mock_post.id = "test123"
        mock_post.title = "How to learn Python?"
        mock_post.selftext = "I'm a beginner looking for Python learning resources."
        mock_post.subreddit = "learnprogramming"
        mock_post.score = 10
        return mock_post
    
    @pytest.fixture
    def mock_analysis_result(self):
        """Mock analysis result"""
        mock_analysis = Mock()
        mock_analysis.topics = ["python", "learning"]
        mock_analysis.keywords = ["python", "beginner", "resources"]
        mock_analysis.sentiment_label = "positive"
        mock_analysis.relevance_score = 0.8
        mock_analysis.sentiment_score = 0.3
        mock_analysis.analysis_metadata = {"post_age_hours": 2}
        return mock_analysis
    
    def test_initialization(self, comment_generator):
        """Test comment generator initialization"""
        assert comment_generator.config is not None
        assert comment_generator.prompts_config is not None
        assert 'rule_based' in comment_generator.backends
        assert comment_generator.fallback_templates is not None
    
    def test_rule_based_backend_initialization(self):
        """Test rule-based backend initialization"""
        templates = {
            'generic': ['Test comment 1', 'Test comment 2'],
            'programming': ['Programming comment']
        }
        backend = RuleBasedBackend(templates)
        
        assert backend.is_available() == True
        assert backend.templates == templates
    
    def test_rule_based_backend_generation(self):
        """Test rule-based comment generation"""
        templates = {
            'programming': ['Great question about {topic}!'],
            'generic': ['Thanks for sharing!']
        }
        backend = RuleBasedBackend(templates)
        
        request = CommentRequest(
            post_title="Python question",
            post_content="How to use loops?",
            subreddit="programming",
            comment_style="helpful",
            topics=["programming"],
            context={},
            max_length=200
        )
        
        response = backend.generate_comment(request)
        
        assert response is not None
        assert isinstance(response, CommentResponse)
        assert response.backend_used == "rule_based"
        assert len(response.comment_text) > 0
    
    def test_template_category_selection(self):
        """Test template category selection logic"""
        templates = {
            'programming': ['Programming comment'],
            'career': ['Career comment'],
            'question': ['Question comment'],
            'generic': ['Generic comment']
        }
        backend = RuleBasedBackend(templates)
        
        # Test programming topic
        request = CommentRequest(
            post_title="Python help",
            post_content="Need help",
            subreddit="test",
            comment_style="helpful",
            topics=["programming"],
            context={}
        )
        category = backend._select_template_category(request)
        assert category == "programming"
        
        # Test question detection
        request.topics = []
        request.post_title = "How to do this?"
        category = backend._select_template_category(request)
        assert category == "question"
        
        # Test generic fallback
        request.post_title = "Random post"
        category = backend._select_template_category(request)
        assert category == "generic"
    
    def test_template_filling(self):
        """Test template variable filling"""
        templates = {'generic': ['Test comment']}
        backend = RuleBasedBackend(templates)
        
        template = "This is about {topic} in r/{subreddit}"
        request = CommentRequest(
            post_title="Test",
            post_content="Test",
            subreddit="test",
            comment_style="helpful",
            topics=["python", "programming"],
            context={}
        )
        
        filled = backend._fill_template(template, request)
        assert "python, programming" in filled
        assert "r/test" in filled
    
    @patch.dict('os.environ', {'GROQ_API_KEY': 'test_key'})
    def test_groq_backend_initialization(self):
        """Test Groq backend initialization"""
        with patch('comment_generator.GROQ_AVAILABLE', True):
            with patch('comment_generator.Groq') as mock_groq:
                mock_client = Mock()
                mock_groq.return_value = mock_client
                
                backend = GroqBackend('test_key')
                assert backend.client == mock_client
                assert backend.is_available() == True
    
    def test_groq_backend_unavailable(self):
        """Test Groq backend when unavailable"""
        backend = GroqBackend('')
        assert backend.is_available() == False
        
        request = Mock()
        response = backend.generate_comment(request)
        assert response is None
    
    @patch.dict('os.environ', {'OPENAI_API_KEY': 'test_key'})
    def test_openai_backend_initialization(self):
        """Test OpenAI backend initialization"""
        with patch('comment_generator.OPENAI_AVAILABLE', True):
            with patch('comment_generator.openai.OpenAI') as mock_openai:
                mock_client = Mock()
                mock_openai.return_value = mock_client
                
                backend = OpenAIBackend('test_key')
                assert backend.client == mock_client
                assert backend.is_available() == True
    
    def test_local_transformer_backend_unavailable(self):
        """Test local transformer backend when transformers unavailable"""
        with patch('comment_generator.TRANSFORMERS_AVAILABLE', False):
            backend = LocalTransformerBackend()
            assert backend.is_available() == False
    
    def test_comment_generation_success(self, comment_generator, mock_post_data, mock_analysis_result):
        """Test successful comment generation"""
        subreddit_config = {'comment_style': 'helpful'}
        
        response = comment_generator.generate_comment(
            mock_post_data, mock_analysis_result, subreddit_config
        )
        
        assert response is not None
        assert isinstance(response, CommentResponse)
        assert response.backend_used == "rule_based"
        assert len(response.comment_text) > 0
    
    def test_comment_validation_success(self, comment_generator, sample_request):
        """Test comment validation with valid comment"""
        valid_comment = "This is a helpful comment that provides value to the discussion and meets all requirements."
        
        is_valid = comment_generator._validate_comment(valid_comment, sample_request)
        assert is_valid == True
    
    def test_comment_validation_too_short(self, comment_generator, sample_request):
        """Test comment validation with too short comment"""
        short_comment = "Too short"
        
        is_valid = comment_generator._validate_comment(short_comment, sample_request)
        assert is_valid == False
    
    def test_comment_validation_too_long(self, comment_generator, sample_request):
        """Test comment validation with too long comment"""
        long_comment = "x" * 1000  # Exceeds max length
        
        is_valid = comment_generator._validate_comment(long_comment, sample_request)
        assert is_valid == False
    
    def test_comment_validation_empty(self, comment_generator, sample_request):
        """Test comment validation with empty comment"""
        empty_comment = ""
        
        is_valid = comment_generator._validate_comment(empty_comment, sample_request)
        assert is_valid == False
    
    def test_inappropriate_content_detection(self, comment_generator):
        """Test inappropriate content detection"""
        inappropriate_comments = [
            "This is spam click here for free money",
            "You're so stupid and idiotic",
            "Let's discuss politics and religion"
        ]
        
        for comment in inappropriate_comments:
            is_inappropriate = comment_generator._contains_inappropriate_content(comment)
            assert is_inappropriate == True
        
        # Test appropriate comment
        appropriate_comment = "This is a helpful and constructive comment."
        is_inappropriate = comment_generator._contains_inappropriate_content(appropriate_comment)
        assert is_inappropriate == False
    
    def test_generic_comment_detection(self, comment_generator):
        """Test generic comment detection"""
        generic_comment = "Thanks for sharing this great post with interesting question and good point"
        
        is_generic = comment_generator._is_too_generic(generic_comment)
        assert is_generic == True
        
        # Test specific comment
        specific_comment = "I recommend using pandas for data manipulation and matplotlib for visualization when working with Python data science projects."
        is_generic = comment_generator._is_too_generic(specific_comment)
        assert is_generic == False
    
    def test_backend_order_configuration(self, comment_generator):
        """Test backend order configuration"""
        backend_order = comment_generator._get_backend_order()
        
        assert isinstance(backend_order, list)
        assert len(backend_order) > 0
        assert 'rule_based' in backend_order
        assert backend_order[-1] == 'rule_based'  # Should always be last
    
    def test_comment_request_building(self, comment_generator, mock_post_data, mock_analysis_result):
        """Test comment request building"""
        subreddit_config = {
            'comment_style': 'technical',
            'max_comments_per_day': 5
        }
        
        request = comment_generator._build_comment_request(
            mock_post_data, mock_analysis_result, subreddit_config
        )
        
        assert isinstance(request, CommentRequest)
        assert request.post_title == mock_post_data.title
        assert request.post_content == mock_post_data.selftext
        assert request.subreddit == mock_post_data.subreddit
        assert request.comment_style == 'technical'
        assert request.topics == mock_analysis_result.topics
    
    def test_backend_status_reporting(self, comment_generator):
        """Test backend status reporting"""
        status = comment_generator.get_backend_status()
        
        assert isinstance(status, dict)
        assert 'rule_based' in status
        assert status['rule_based'] == True  # Rule-based should always be available
    
    def test_fallback_template_loading(self, comment_generator):
        """Test fallback template loading"""
        templates = comment_generator.fallback_templates
        
        assert isinstance(templates, dict)
        assert 'generic' in templates
        assert isinstance(templates['generic'], list)
        assert len(templates['generic']) > 0
    
    def test_error_handling_in_generation(self, comment_generator, mock_post_data, mock_analysis_result):
        """Test error handling during comment generation"""
        # Mock backend to raise exception
        mock_backend = Mock()
        mock_backend.is_available.return_value = True
        mock_backend.generate_comment.side_effect = Exception("Backend error")
        
        comment_generator.backends['test_backend'] = mock_backend
        comment_generator.config['primary_backend'] = 'test_backend'
        comment_generator.config['fallback_backends'] = ['rule_based']
        
        subreddit_config = {'comment_style': 'helpful'}
        
        # Should fallback to rule_based backend
        response = comment_generator.generate_comment(
            mock_post_data, mock_analysis_result, subreddit_config
        )
        
        assert response is not None
        assert response.backend_used == "rule_based"
    
    def test_multiple_backend_fallback(self, comment_generator):
        """Test multiple backend fallback chain"""
        # Create mock backends that fail
        failing_backend1 = Mock()
        failing_backend1.is_available.return_value = True
        failing_backend1.generate_comment.return_value = None
        
        failing_backend2 = Mock()
        failing_backend2.is_available.return_value = False
        
        comment_generator.backends['failing1'] = failing_backend1
        comment_generator.backends['failing2'] = failing_backend2
        
        # Test backend order with failures
        backend_order = ['failing1', 'failing2', 'rule_based']
        
        with patch.object(comment_generator, '_get_backend_order', return_value=backend_order):
            mock_post_data = Mock()
            mock_post_data.title = "Test"
            mock_post_data.selftext = "Test content"
            mock_post_data.subreddit = "test"
            mock_post_data.score = 10
            
            mock_analysis_result = Mock()
            mock_analysis_result.topics = ["test"]
            mock_analysis_result.keywords = ["test"]
            mock_analysis_result.sentiment_label = "positive"
            mock_analysis_result.relevance_score = 0.8
            mock_analysis_result.sentiment_score = 0.3
            mock_analysis_result.analysis_metadata = {"post_age_hours": 2}
            
            subreddit_config = {'comment_style': 'helpful'}
            
            response = comment_generator.generate_comment(
                mock_post_data, mock_analysis_result, subreddit_config
            )
            
            # Should eventually succeed with rule_based backend
            assert response is not None
            assert response.backend_used == "rule_based"
