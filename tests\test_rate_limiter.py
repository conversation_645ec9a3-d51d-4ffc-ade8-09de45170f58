"""
Tests for RateLimiter module
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
import tempfile
import os

from rate_limiter import RateLimiter, RateLimit, ActionRecord


class TestRateLimiter:
    """Test cases for RateLimiter"""
    
    @pytest.fixture
    def rate_config(self):
        """Test configuration for rate limiter"""
        return {
            'global_comments_per_hour': 5,
            'global_comments_per_day': 20,
            'subreddit_comments_per_hour': 2,
            'subreddit_comments_per_day': 8,
            'comment_cooldown_minutes': 10,
            'error_cooldown_minutes': 30,
            'rate_limit_cooldown_minutes': 60,
            'enable_adaptive_limits': True,
            'karma_threshold_adjustment': True,
            'downvote_penalty_multiplier': 2.0
        }
    
    @pytest.fixture
    def rate_limiter(self, rate_config):
        """Create RateLimiter instance for testing"""
        return RateLimiter(rate_config)
    
    def test_initialization(self, rate_limiter, rate_config):
        """Test rate limiter initialization"""
        assert rate_limiter.global_limits.max_per_hour == rate_config['global_comments_per_hour']
        assert rate_limiter.global_limits.max_per_day == rate_config['global_comments_per_day']
        assert rate_limiter.adaptive_enabled == rate_config['enable_adaptive_limits']
    
    def test_can_perform_action_initial(self, rate_limiter):
        """Test that actions are allowed initially"""
        can_perform, reason = rate_limiter.can_perform_action('test_subreddit')
        assert can_perform
        assert reason == "Action allowed"
    
    def test_global_rate_limit_hour(self, rate_limiter):
        """Test global hourly rate limiting"""
        subreddit = 'test_subreddit'
        
        # Perform actions up to the limit
        for i in range(5):  # global_comments_per_hour = 5
            can_perform, _ = rate_limiter.can_perform_action(subreddit)
            assert can_perform
            
            rate_limiter.record_action(subreddit, f'post_{i}', 'comment', True)
        
        # Next action should be blocked by global limit
        can_perform, reason = rate_limiter.can_perform_action(subreddit)
        assert not can_perform
        assert "Global rate limits exceeded" in reason
    
    def test_global_rate_limit_day(self, rate_limiter):
        """Test global daily rate limiting"""
        subreddit = 'test_subreddit'
        
        # Simulate 20 successful actions (global_comments_per_day = 20)
        rate_limiter.global_limits.current_day_count = 20
        
        can_perform, reason = rate_limiter.can_perform_action(subreddit)
        assert not can_perform
        assert "Global rate limits exceeded" in reason
    
    def test_subreddit_rate_limit(self, rate_limiter):
        """Test subreddit-specific rate limiting"""
        subreddit = 'test_subreddit'
        
        # Perform actions up to subreddit limit
        for i in range(2):  # subreddit_comments_per_hour = 2
            can_perform, _ = rate_limiter.can_perform_action(subreddit)
            assert can_perform
            
            rate_limiter.record_action(subreddit, f'post_{i}', 'comment', True)
        
        # Next action should be blocked by subreddit limit
        can_perform, reason = rate_limiter.can_perform_action(subreddit)
        assert not can_perform
        assert f"Subreddit rate limits exceeded for r/{subreddit}" in reason
    
    def test_cooldown_mechanism(self, rate_limiter):
        """Test cooldown mechanism"""
        subreddit = 'test_subreddit'
        
        # Record a successful action
        rate_limiter.record_action(subreddit, 'post_1', 'comment', True)
        
        # Should be in cooldown
        can_perform, reason = rate_limiter.can_perform_action(subreddit)
        assert not can_perform
        assert "cooldown active" in reason.lower()
    
    def test_failed_action_handling(self, rate_limiter):
        """Test handling of failed actions"""
        subreddit = 'test_subreddit'
        
        # Record a failed action with rate limit error
        rate_limiter.record_action(
            subreddit, 'post_1', 'comment', False, 
            error_message="Rate limit exceeded"
        )
        
        # Should have extended cooldown
        can_perform, reason = rate_limiter.can_perform_action(subreddit)
        assert not can_perform
        assert "cooldown active" in reason.lower()
    
    def test_adaptive_limits_negative_karma(self, rate_limiter):
        """Test adaptive limits with negative karma"""
        subreddit = 'test_subreddit'
        
        # Record multiple actions with negative karma
        for i in range(3):
            rate_limiter.record_action(
                subreddit, f'post_{i}', 'comment', True, response_score=-5
            )
        
        # Should trigger adaptive limits
        can_perform, reason = rate_limiter.can_perform_action(subreddit)
        # Note: This might still be allowed depending on other factors
        # The test verifies the adaptive logic is called
        assert isinstance(can_perform, bool)
    
    def test_adaptive_limits_low_success_rate(self, rate_limiter):
        """Test adaptive limits with low success rate"""
        subreddit = 'test_subreddit'
        
        # Record mostly failed actions
        for i in range(5):
            success = i < 2  # Only first 2 succeed
            rate_limiter.record_action(subreddit, f'post_{i}', 'comment', success)
        
        # Should trigger adaptive limits due to low success rate
        can_perform, reason = rate_limiter.can_perform_action(subreddit)
        # The adaptive check should be triggered
        assert isinstance(can_perform, bool)
    
    def test_subreddit_limit_creation(self, rate_limiter):
        """Test automatic creation of subreddit limits"""
        subreddit = 'new_subreddit'
        
        # Should create new limit automatically
        limit = rate_limiter._get_subreddit_limit(subreddit)
        assert isinstance(limit, RateLimit)
        assert limit.max_per_hour == 2  # Default from config
        assert limit.max_per_day == 8   # Default from config
    
    def test_rate_limit_reset(self, rate_limiter):
        """Test rate limit counter reset"""
        current_time = datetime.now()
        
        # Set up limits as if they were from an hour ago
        rate_limiter.global_limits.last_reset_hour = current_time - timedelta(hours=2)
        rate_limiter.global_limits.current_hour_count = 5
        
        # Update should reset the counter
        rate_limiter._update_rate_limits(current_time)
        assert rate_limiter.global_limits.current_hour_count == 0
    
    def test_action_record_creation(self, rate_limiter):
        """Test action record creation and storage"""
        subreddit = 'test_subreddit'
        post_id = 'test_post'
        
        initial_count = len(rate_limiter.action_history)
        
        rate_limiter.record_action(subreddit, post_id, 'comment', True, response_score=5)
        
        assert len(rate_limiter.action_history) == initial_count + 1
        
        latest_record = rate_limiter.action_history[-1]
        assert latest_record.subreddit == subreddit
        assert latest_record.post_id == post_id
        assert latest_record.success == True
        assert latest_record.response_score == 5
    
    def test_get_status(self, rate_limiter):
        """Test status reporting"""
        status = rate_limiter.get_status()
        
        assert 'global_limits' in status
        assert 'subreddit_limits' in status
        assert 'recent_actions' in status
        assert 'success_rate_24h' in status
        assert 'adaptive_enabled' in status
        
        # Check global limits structure
        global_limits = status['global_limits']
        assert 'hour_remaining' in global_limits
        assert 'day_remaining' in global_limits
        assert 'cooldown_remaining' in global_limits
    
    def test_reset_limits(self, rate_limiter):
        """Test manual limit reset"""
        subreddit = 'test_subreddit'
        
        # Set up some usage
        rate_limiter.global_limits.current_hour_count = 3
        rate_limiter.global_limits.current_day_count = 10
        rate_limiter._get_subreddit_limit(subreddit).current_hour_count = 2
        
        # Reset all limits
        rate_limiter.reset_limits()
        
        assert rate_limiter.global_limits.current_hour_count == 0
        assert rate_limiter.global_limits.current_day_count == 0
        assert len(rate_limiter.cooldowns) == 0
        
        # Reset specific subreddit
        rate_limiter._get_subreddit_limit(subreddit).current_hour_count = 2
        rate_limiter.reset_limits(subreddit)
        
        assert rate_limiter._get_subreddit_limit(subreddit).current_hour_count == 0
    
    def test_success_rate_calculation(self, rate_limiter):
        """Test success rate calculation"""
        subreddit = 'test_subreddit'
        
        # Record mixed success/failure actions
        for i in range(10):
            success = i < 7  # 70% success rate
            rate_limiter.record_action(subreddit, f'post_{i}', 'comment', success)
        
        success_rate = rate_limiter._calculate_success_rate(24)
        assert 0.6 <= success_rate <= 0.8  # Should be around 70%
    
    def test_state_persistence_file(self, rate_limiter, rate_config):
        """Test state persistence to file"""
        # Create a temporary file for testing
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json') as f:
            temp_file = f.name
        
        try:
            # Mock the state file path
            with patch.object(rate_limiter, 'database_manager', None):
                # Record some actions
                rate_limiter.record_action('test', 'post1', 'comment', True)
                
                # Save state should work without database manager
                rate_limiter._save_state()
                
                # Create new rate limiter and load state
                new_limiter = RateLimiter(rate_config)
                
                # Should have loaded some state
                assert isinstance(new_limiter.global_limits, RateLimit)
        
        finally:
            # Clean up
            if os.path.exists(temp_file):
                os.unlink(temp_file)
    
    def test_error_handling(self, rate_limiter):
        """Test error handling in various scenarios"""
        # Test with invalid subreddit name
        can_perform, reason = rate_limiter.can_perform_action('')
        assert isinstance(can_perform, bool)
        assert isinstance(reason, str)
        
        # Test with None values
        rate_limiter.record_action('test', 'post1', 'comment', True, response_score=None)
        
        # Should not crash
        status = rate_limiter.get_status()
        assert isinstance(status, dict)
