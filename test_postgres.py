#!/usr/bin/env python3
"""
Test script to verify PostgreSQL integration for RedditSage
"""

import os
import sys
from datetime import datetime

# Add src to path
sys.path.insert(0, 'src')

from database import DatabaseManager, CommentRecord
from config import Config

def test_postgres_connection():
    """Test PostgreSQL connection and basic operations"""
    print("🔍 Testing PostgreSQL Integration for RedditSage")
    print("=" * 50)
    
    try:
        # Load configuration
        print("📋 Loading configuration...")
        config = Config()
        db_config = config.get_database_config()
        print(f"   Database: {db_config.get('database', 'reddit_sage')}")
        print(f"   Host: {db_config.get('host', 'localhost')}")
        print(f"   Port: {db_config.get('port', '5432')}")
        print(f"   User: {db_config.get('user', 'reddit_sage')}")
        
        # Test database connection
        print("\n🔌 Testing database connection...")
        db_manager = DatabaseManager(db_config)
        print("   ✅ Database connection successful!")
        
        # Test table creation
        print("\n📊 Testing table operations...")
        print("   ✅ Tables created successfully!")
        
        # Test comment record insertion
        print("\n💬 Testing comment record operations...")
        test_record = CommentRecord(
            id=None,
            post_id="test_post_123",
            subreddit="test",
            comment_id="test_comment_123",
            comment_text="This is a test comment for PostgreSQL integration",
            posted_at=datetime.now(),
            post_title="Test Post for PostgreSQL",
            post_score=10,
            comment_score=5,
            success=True,
            error_message=None,
            ai_backend="test_backend",
            generation_time=1.5,
            relevance_score=0.8,
            sentiment_score=0.3
        )
        
        # Save test record
        record_id = db_manager.save_comment(test_record)
        print(f"   ✅ Comment record saved with ID: {record_id}")
        
        # Retrieve recent comments
        recent_comments = db_manager.get_recent_comments(hours=1)
        print(f"   ✅ Retrieved {len(recent_comments)} recent comments")
        
        # Test analytics
        analytics = db_manager.get_analytics_data(days=1)
        print(f"   ✅ Analytics generated: {analytics.total_comments} total comments")
        
        # Test configuration storage
        print("\n⚙️ Testing configuration storage...")
        db_manager.save_config("test_key", {"test": "value"})
        loaded_config = db_manager.load_config("test_key")
        print(f"   ✅ Configuration saved and loaded: {loaded_config}")
        
        # Test database stats
        print("\n📈 Testing database statistics...")
        stats = db_manager.get_database_stats()
        print(f"   ✅ Database stats: {stats['total_comments']} comments, {stats['tracked_subreddits']} subreddits")
        
        print("\n🎉 All PostgreSQL tests passed successfully!")
        print("✅ RedditSage is ready to use with PostgreSQL!")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure psycopg2-binary is installed: pip install psycopg2-binary")
        return False
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("\n💡 Troubleshooting steps:")
        print("1. Ensure PostgreSQL is running")
        print("2. Check database credentials in .env file")
        print("3. Verify database 'reddit_sage' exists")
        print("4. Ensure user 'reddit_sage' has proper permissions")
        print("\n🐳 To start PostgreSQL with Docker:")
        print("docker run --name reddit-sage-postgres \\")
        print("  -e POSTGRES_DB=reddit_sage \\")
        print("  -e POSTGRES_USER=reddit_sage \\")
        print("  -e POSTGRES_PASSWORD=reddit_sage_password \\")
        print("  -p 5432:5432 -d postgres:15-alpine")
        return False

def test_without_postgres():
    """Test configuration loading without database connection"""
    print("\n🔧 Testing configuration without database...")
    try:
        config = Config()
        print("   ✅ Configuration loaded successfully")
        
        reddit_config = config.get_reddit_config()
        print(f"   ✅ Reddit config: {len(reddit_config)} settings")
        
        ai_config = config.get_ai_config()
        print(f"   ✅ AI config: {len(ai_config)} settings")
        
        enabled_subreddits = config.get_enabled_subreddits()
        print(f"   ✅ Enabled subreddits: {len(enabled_subreddits)} subreddits")
        
        return True
    except Exception as e:
        print(f"   ❌ Configuration test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 RedditSage PostgreSQL Integration Test")
    print("=" * 60)
    
    # Test configuration first
    config_success = test_without_postgres()
    
    if config_success:
        # Test PostgreSQL connection
        db_success = test_postgres_connection()
        
        if db_success:
            print("\n🎯 Summary: All tests passed! RedditSage is ready for production.")
            sys.exit(0)
        else:
            print("\n⚠️  Summary: Configuration works, but PostgreSQL connection failed.")
            print("   The bot can run with proper database setup.")
            sys.exit(1)
    else:
        print("\n❌ Summary: Configuration test failed.")
        sys.exit(1)
