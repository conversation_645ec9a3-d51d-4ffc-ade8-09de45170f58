"""
Setup script for RedditSage - AI-Powered Reddit Auto-Comment Bot
"""

from setuptools import setup, find_packages
import os

# Read the README file
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="reddit-sage",
    version="1.0.0",
    author="HectorTa1989",
    author_email="<EMAIL>",
    description="AI-Powered Reddit Auto-Comment Bot",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/HectorTa1989/reddit-auto-comment",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Communications :: Chat",
        "Topic :: Internet :: WWW/HTTP :: Dynamic Content",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.4.3",
            "pytest-cov>=4.1.0",
            "black>=23.12.1",
            "flake8>=6.1.0",
            "mypy>=1.8.0",
        ],
        "local-ai": [
            "torch>=2.1.2",
            "transformers>=4.36.2",
            "sentence-transformers>=2.2.2",
        ],
        "monitoring": [
            "prometheus-client>=0.19.0",
            "grafana-api>=1.0.3",
        ]
    },
    entry_points={
        "console_scripts": [
            "reddit-sage=src.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["config/*.yaml", "data/*.db"],
    },
    keywords=[
        "reddit", "bot", "ai", "automation", "nlp", "machine-learning",
        "social-media", "comment-generation", "content-analysis"
    ],
    project_urls={
        "Bug Reports": "https://github.com/HectorTa1989/reddit-auto-comment/issues",
        "Source": "https://github.com/HectorTa1989/reddit-auto-comment",
        "Documentation": "https://github.com/HectorTa1989/reddit-auto-comment/wiki",
    },
)
