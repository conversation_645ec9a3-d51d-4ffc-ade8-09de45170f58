"""
Database Manager for Reddit Bot

This module provides comprehensive database operations using SQLite for
tracking comments, analytics, and bot state management.
"""

import sqlite3
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, <PERSON>ple
from dataclasses import dataclass
from contextlib import contextmanager
from loguru import logger


@dataclass
class CommentRecord:
    """Database record for posted comments"""
    id: Optional[int]
    post_id: str
    subreddit: str
    comment_id: Optional[str]
    comment_text: str
    posted_at: datetime
    post_title: str
    post_score: int
    comment_score: Optional[int]
    success: bool
    error_message: Optional[str]
    ai_backend: str
    generation_time: float
    relevance_score: float
    sentiment_score: float


@dataclass
class AnalyticsData:
    """Analytics data structure"""
    total_comments: int
    successful_comments: int
    success_rate: float
    average_karma: float
    top_subreddits: List[Tuple[str, int]]
    backend_performance: Dict[str, Dict[str, Any]]
    daily_activity: List[Tuple[str, int]]


class DatabaseManager:
    """
    SQLite database manager for Reddit bot operations
    """
    
    def __init__(self, db_path: str = "data/comments.db"):
        """
        Initialize database manager
        
        Args:
            db_path: Path to SQLite database file
        """
        self.db_path = db_path
        self.ensure_directory_exists()
        self.initialize_database()
        logger.info(f"Database manager initialized with {db_path}")
    
    def ensure_directory_exists(self):
        """Ensure the database directory exists"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
    
    @contextmanager
    def get_connection(self):
        """Context manager for database connections"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # Enable column access by name
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def initialize_database(self):
        """Initialize database tables"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Comments table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS comments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    post_id TEXT NOT NULL,
                    subreddit TEXT NOT NULL,
                    comment_id TEXT,
                    comment_text TEXT NOT NULL,
                    posted_at TIMESTAMP NOT NULL,
                    post_title TEXT NOT NULL,
                    post_score INTEGER NOT NULL,
                    comment_score INTEGER,
                    success BOOLEAN NOT NULL,
                    error_message TEXT,
                    ai_backend TEXT NOT NULL,
                    generation_time REAL NOT NULL,
                    relevance_score REAL NOT NULL,
                    sentiment_score REAL NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Rate limiter state table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS rate_limiter_state (
                    id INTEGER PRIMARY KEY,
                    state_data TEXT NOT NULL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Bot configuration table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS bot_config (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Analytics cache table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS analytics_cache (
                    cache_key TEXT PRIMARY KEY,
                    cache_data TEXT NOT NULL,
                    expires_at TIMESTAMP NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Subreddit performance table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS subreddit_performance (
                    subreddit TEXT PRIMARY KEY,
                    total_comments INTEGER DEFAULT 0,
                    successful_comments INTEGER DEFAULT 0,
                    total_karma INTEGER DEFAULT 0,
                    average_karma REAL DEFAULT 0.0,
                    last_comment_at TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create indexes for better performance
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_comments_subreddit ON comments(subreddit)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_comments_posted_at ON comments(posted_at)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_comments_success ON comments(success)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_comments_post_id ON comments(post_id)")
            
            conn.commit()
            logger.info("Database tables initialized successfully")
    
    def save_comment(self, comment_record: CommentRecord) -> int:
        """
        Save a comment record to the database
        
        Args:
            comment_record: CommentRecord object to save
        
        Returns:
            ID of the inserted record
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO comments (
                    post_id, subreddit, comment_id, comment_text, posted_at,
                    post_title, post_score, comment_score, success, error_message,
                    ai_backend, generation_time, relevance_score, sentiment_score
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                comment_record.post_id,
                comment_record.subreddit,
                comment_record.comment_id,
                comment_record.comment_text,
                comment_record.posted_at,
                comment_record.post_title,
                comment_record.post_score,
                comment_record.comment_score,
                comment_record.success,
                comment_record.error_message,
                comment_record.ai_backend,
                comment_record.generation_time,
                comment_record.relevance_score,
                comment_record.sentiment_score
            ))
            
            record_id = cursor.lastrowid
            conn.commit()
            
            # Update subreddit performance
            self._update_subreddit_performance(comment_record)
            
            logger.info(f"Saved comment record with ID {record_id}")
            return record_id
    
    def update_comment_score(self, comment_id: str, score: int):
        """
        Update the karma score for a comment
        
        Args:
            comment_id: Reddit comment ID
            score: Current karma score
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE comments 
                SET comment_score = ? 
                WHERE comment_id = ?
            """, (score, comment_id))
            
            conn.commit()
            
            if cursor.rowcount > 0:
                logger.info(f"Updated score for comment {comment_id}: {score}")
    
    def get_recent_comments(self, hours: int = 24, subreddit: Optional[str] = None) -> List[CommentRecord]:
        """
        Get recent comments from the database
        
        Args:
            hours: Number of hours to look back
            subreddit: Optional subreddit filter
        
        Returns:
            List of CommentRecord objects
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            if subreddit:
                cursor.execute("""
                    SELECT * FROM comments 
                    WHERE posted_at > ? AND subreddit = ?
                    ORDER BY posted_at DESC
                """, (cutoff_time, subreddit))
            else:
                cursor.execute("""
                    SELECT * FROM comments 
                    WHERE posted_at > ?
                    ORDER BY posted_at DESC
                """, (cutoff_time,))
            
            rows = cursor.fetchall()
            return [self._row_to_comment_record(row) for row in rows]
    
    def get_analytics_data(self, days: int = 30) -> AnalyticsData:
        """
        Generate analytics data for the specified period
        
        Args:
            days: Number of days to analyze
        
        Returns:
            AnalyticsData object with comprehensive statistics
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # Total and successful comments
            cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful
                FROM comments 
                WHERE posted_at > ?
            """, (cutoff_date,))
            
            total, successful = cursor.fetchone()
            success_rate = (successful / total) if total > 0 else 0.0
            
            # Average karma
            cursor.execute("""
                SELECT AVG(comment_score) 
                FROM comments 
                WHERE posted_at > ? AND comment_score IS NOT NULL AND success = 1
            """, (cutoff_date,))
            
            avg_karma_result = cursor.fetchone()[0]
            avg_karma = avg_karma_result if avg_karma_result is not None else 0.0
            
            # Top subreddits
            cursor.execute("""
                SELECT subreddit, COUNT(*) as count
                FROM comments 
                WHERE posted_at > ? AND success = 1
                GROUP BY subreddit 
                ORDER BY count DESC 
                LIMIT 10
            """, (cutoff_date,))
            
            top_subreddits = cursor.fetchall()
            
            # Backend performance
            cursor.execute("""
                SELECT 
                    ai_backend,
                    COUNT(*) as total,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful,
                    AVG(generation_time) as avg_time,
                    AVG(CASE WHEN comment_score IS NOT NULL THEN comment_score END) as avg_karma
                FROM comments 
                WHERE posted_at > ?
                GROUP BY ai_backend
            """, (cutoff_date,))
            
            backend_rows = cursor.fetchall()
            backend_performance = {}
            for row in backend_rows:
                backend_performance[row[0]] = {
                    'total': row[1],
                    'successful': row[2],
                    'success_rate': (row[2] / row[1]) if row[1] > 0 else 0.0,
                    'avg_generation_time': row[3] or 0.0,
                    'avg_karma': row[4] or 0.0
                }
            
            # Daily activity
            cursor.execute("""
                SELECT 
                    DATE(posted_at) as date,
                    COUNT(*) as count
                FROM comments 
                WHERE posted_at > ? AND success = 1
                GROUP BY DATE(posted_at)
                ORDER BY date DESC
                LIMIT 30
            """, (cutoff_date,))
            
            daily_activity = cursor.fetchall()
            
            return AnalyticsData(
                total_comments=total or 0,
                successful_comments=successful or 0,
                success_rate=success_rate,
                average_karma=avg_karma,
                top_subreddits=top_subreddits,
                backend_performance=backend_performance,
                daily_activity=daily_activity
            )

    def save_rate_limiter_state(self, state_data: Dict[str, Any]):
        """
        Save rate limiter state to database

        Args:
            state_data: Rate limiter state dictionary
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # Delete existing state and insert new one
            cursor.execute("DELETE FROM rate_limiter_state")
            cursor.execute("""
                INSERT INTO rate_limiter_state (state_data)
                VALUES (?)
            """, (json.dumps(state_data),))

            conn.commit()
            logger.debug("Saved rate limiter state to database")

    def load_rate_limiter_state(self) -> Optional[Dict[str, Any]]:
        """
        Load rate limiter state from database

        Returns:
            Rate limiter state dictionary or None if not found
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()

            cursor.execute("""
                SELECT state_data FROM rate_limiter_state
                ORDER BY updated_at DESC
                LIMIT 1
            """)

            row = cursor.fetchone()
            if row:
                return json.loads(row[0])
            return None

    def save_config(self, key: str, value: Any):
        """
        Save configuration value to database

        Args:
            key: Configuration key
            value: Configuration value (will be JSON serialized)
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()

            cursor.execute("""
                INSERT OR REPLACE INTO bot_config (key, value)
                VALUES (?, ?)
            """, (key, json.dumps(value)))

            conn.commit()
            logger.debug(f"Saved config: {key}")

    def load_config(self, key: str, default: Any = None) -> Any:
        """
        Load configuration value from database

        Args:
            key: Configuration key
            default: Default value if key not found

        Returns:
            Configuration value or default
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()

            cursor.execute("SELECT value FROM bot_config WHERE key = ?", (key,))
            row = cursor.fetchone()

            if row:
                return json.loads(row[0])
            return default

    def get_subreddit_stats(self, subreddit: str) -> Dict[str, Any]:
        """
        Get statistics for a specific subreddit

        Args:
            subreddit: Subreddit name

        Returns:
            Dictionary with subreddit statistics
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # Get basic stats
            cursor.execute("""
                SELECT
                    COUNT(*) as total_comments,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_comments,
                    AVG(CASE WHEN comment_score IS NOT NULL THEN comment_score END) as avg_karma,
                    MAX(posted_at) as last_comment
                FROM comments
                WHERE subreddit = ?
            """, (subreddit,))

            row = cursor.fetchone()

            return {
                'subreddit': subreddit,
                'total_comments': row[0] or 0,
                'successful_comments': row[1] or 0,
                'success_rate': (row[1] / row[0]) if row[0] > 0 else 0.0,
                'average_karma': row[2] or 0.0,
                'last_comment': row[3]
            }

    def cleanup_old_records(self, days: int = 30):
        """
        Clean up old records from the database

        Args:
            days: Number of days to keep records
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()

            cutoff_date = datetime.now() - timedelta(days=days)

            # Clean up old comments
            cursor.execute("""
                DELETE FROM comments
                WHERE posted_at < ?
            """, (cutoff_date,))

            deleted_comments = cursor.rowcount

            # Clean up expired analytics cache
            cursor.execute("""
                DELETE FROM analytics_cache
                WHERE expires_at < ?
            """, (datetime.now(),))

            deleted_cache = cursor.rowcount

            conn.commit()

            logger.info(f"Cleaned up {deleted_comments} old comments and {deleted_cache} expired cache entries")

    def _update_subreddit_performance(self, comment_record: CommentRecord):
        """Update subreddit performance statistics"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # Get current stats
            cursor.execute("""
                SELECT total_comments, successful_comments, total_karma
                FROM subreddit_performance
                WHERE subreddit = ?
            """, (comment_record.subreddit,))

            row = cursor.fetchone()

            if row:
                # Update existing record
                total_comments = row[0] + 1
                successful_comments = row[1] + (1 if comment_record.success else 0)
                total_karma = row[2] + (comment_record.comment_score or 0)
                avg_karma = total_karma / successful_comments if successful_comments > 0 else 0.0

                cursor.execute("""
                    UPDATE subreddit_performance
                    SET total_comments = ?, successful_comments = ?,
                        total_karma = ?, average_karma = ?, last_comment_at = ?
                    WHERE subreddit = ?
                """, (total_comments, successful_comments, total_karma, avg_karma,
                      comment_record.posted_at, comment_record.subreddit))
            else:
                # Insert new record
                total_comments = 1
                successful_comments = 1 if comment_record.success else 0
                total_karma = comment_record.comment_score or 0
                avg_karma = total_karma if successful_comments > 0 else 0.0

                cursor.execute("""
                    INSERT INTO subreddit_performance
                    (subreddit, total_comments, successful_comments, total_karma,
                     average_karma, last_comment_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (comment_record.subreddit, total_comments, successful_comments,
                      total_karma, avg_karma, comment_record.posted_at))

            conn.commit()

    def _row_to_comment_record(self, row) -> CommentRecord:
        """Convert database row to CommentRecord object"""
        return CommentRecord(
            id=row['id'],
            post_id=row['post_id'],
            subreddit=row['subreddit'],
            comment_id=row['comment_id'],
            comment_text=row['comment_text'],
            posted_at=datetime.fromisoformat(row['posted_at']),
            post_title=row['post_title'],
            post_score=row['post_score'],
            comment_score=row['comment_score'],
            success=bool(row['success']),
            error_message=row['error_message'],
            ai_backend=row['ai_backend'],
            generation_time=row['generation_time'],
            relevance_score=row['relevance_score'],
            sentiment_score=row['sentiment_score']
        )

    def get_database_stats(self) -> Dict[str, Any]:
        """Get overall database statistics"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # Get table sizes
            cursor.execute("SELECT COUNT(*) FROM comments")
            total_comments = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM subreddit_performance")
            tracked_subreddits = cursor.fetchone()[0]

            # Get database file size
            db_size = os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0

            return {
                'total_comments': total_comments,
                'tracked_subreddits': tracked_subreddits,
                'database_size_mb': db_size / (1024 * 1024),
                'database_path': self.db_path
            }

    def export_data(self, output_file: str, days: int = 30):
        """
        Export data to JSON file

        Args:
            output_file: Output file path
            days: Number of days of data to export
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days)

            with self.get_connection() as conn:
                cursor = conn.cursor()

                # Export comments
                cursor.execute("""
                    SELECT * FROM comments
                    WHERE posted_at > ?
                    ORDER BY posted_at DESC
                """, (cutoff_date,))

                comments = []
                for row in cursor.fetchall():
                    comment_dict = dict(row)
                    # Convert datetime to string for JSON serialization
                    comment_dict['posted_at'] = comment_dict['posted_at']
                    comments.append(comment_dict)

                # Export subreddit performance
                cursor.execute("SELECT * FROM subreddit_performance")
                subreddit_performance = [dict(row) for row in cursor.fetchall()]

                export_data = {
                    'export_date': datetime.now().isoformat(),
                    'days_exported': days,
                    'comments': comments,
                    'subreddit_performance': subreddit_performance,
                    'analytics': self.get_analytics_data(days).__dict__
                }

                with open(output_file, 'w') as f:
                    json.dump(export_data, f, indent=2, default=str)

                logger.info(f"Exported data to {output_file}")

        except Exception as e:
            logger.error(f"Error exporting data: {e}")
            raise
