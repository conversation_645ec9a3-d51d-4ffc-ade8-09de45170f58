"""
AI-Powered Comment Generator

This module provides intelligent comment generation using multiple AI backends
including local models, OpenAI-compatible APIs, and rule-based fallbacks.
"""

import os
import random
import re
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from abc import ABC, abstractmethod
import yaml
from loguru import logger

# AI API clients
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    from groq import Groq
    GROQ_AVAILABLE = True
except ImportError:
    GROQ_AVAILABLE = False

try:
    from transformers import pipeline, AutoTokenizer, AutoModelForCausalLM
    import torch
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False


@dataclass
class CommentRequest:
    """Request structure for comment generation"""
    post_title: str
    post_content: str
    subreddit: str
    comment_style: str
    topics: List[str]
    context: Dict[str, Any]
    max_length: int = 500
    temperature: float = 0.7


@dataclass
class CommentResponse:
    """Response structure for generated comments"""
    comment_text: str
    confidence_score: float
    backend_used: str
    generation_time: float
    metadata: Dict[str, Any]


class AIBackend(ABC):
    """Abstract base class for AI backends"""
    
    @abstractmethod
    def generate_comment(self, request: CommentRequest) -> Optional[CommentResponse]:
        """Generate a comment using this backend"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """Check if this backend is available"""
        pass


class GroqBackend(AIBackend):
    """Groq API backend for comment generation"""
    
    def __init__(self, api_key: str, model: str = "mixtral-8x7b-32768"):
        self.api_key = api_key
        self.model = model
        self.client = None
        
        if GROQ_AVAILABLE and api_key:
            try:
                self.client = Groq(api_key=api_key)
            except Exception as e:
                logger.error(f"Failed to initialize Groq client: {e}")
    
    def is_available(self) -> bool:
        return self.client is not None
    
    def generate_comment(self, request: CommentRequest) -> Optional[CommentResponse]:
        if not self.is_available():
            return None
        
        try:
            import time
            start_time = time.time()
            
            # Build prompt
            prompt = self._build_prompt(request)
            
            # Generate comment
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a helpful Reddit community member."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=min(request.max_length, 500),
                temperature=request.temperature,
                top_p=0.9
            )
            
            comment_text = response.choices[0].message.content.strip()
            generation_time = time.time() - start_time
            
            return CommentResponse(
                comment_text=comment_text,
                confidence_score=0.8,  # Groq generally produces good results
                backend_used="groq",
                generation_time=generation_time,
                metadata={"model": self.model, "tokens_used": response.usage.total_tokens}
            )
            
        except Exception as e:
            logger.error(f"Groq generation error: {e}")
            return None
    
    def _build_prompt(self, request: CommentRequest) -> str:
        """Build prompt for Groq API"""
        return f"""
Generate a helpful, relevant comment for this Reddit post in r/{request.subreddit}.

Post Title: {request.post_title}
Post Content: {request.post_content[:1000]}...

Comment Style: {request.comment_style}
Topics: {', '.join(request.topics)}

Requirements:
- Be helpful and constructive
- Match the subreddit's culture
- Keep it conversational and natural
- Length: 50-{request.max_length} characters
- Add genuine value to the discussion

Generate only the comment text, no explanations:
"""


class OpenAIBackend(AIBackend):
    """OpenAI API backend for comment generation"""
    
    def __init__(self, api_key: str, model: str = "gpt-3.5-turbo"):
        self.api_key = api_key
        self.model = model
        self.client = None
        
        if OPENAI_AVAILABLE and api_key:
            try:
                self.client = openai.OpenAI(api_key=api_key)
            except Exception as e:
                logger.error(f"Failed to initialize OpenAI client: {e}")
    
    def is_available(self) -> bool:
        return self.client is not None
    
    def generate_comment(self, request: CommentRequest) -> Optional[CommentResponse]:
        if not self.is_available():
            return None
        
        try:
            import time
            start_time = time.time()
            
            prompt = self._build_prompt(request)
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a helpful Reddit community member."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=min(request.max_length, 500),
                temperature=request.temperature
            )
            
            comment_text = response.choices[0].message.content.strip()
            generation_time = time.time() - start_time
            
            return CommentResponse(
                comment_text=comment_text,
                confidence_score=0.9,  # OpenAI generally produces high-quality results
                backend_used="openai",
                generation_time=generation_time,
                metadata={"model": self.model, "tokens_used": response.usage.total_tokens}
            )
            
        except Exception as e:
            logger.error(f"OpenAI generation error: {e}")
            return None
    
    def _build_prompt(self, request: CommentRequest) -> str:
        """Build prompt for OpenAI API"""
        return f"""
Generate a helpful Reddit comment for r/{request.subreddit}.

Post: "{request.post_title}"
Content: {request.post_content[:800]}

Style: {request.comment_style}
Topics: {', '.join(request.topics)}

Create a natural, helpful comment that adds value. Be conversational and genuine.
Length: 50-{request.max_length} characters.
"""


class LocalTransformerBackend(AIBackend):
    """Local transformer model backend"""
    
    def __init__(self, model_name: str = "microsoft/DialoGPT-medium"):
        self.model_name = model_name
        self.generator = None
        self.tokenizer = None
        
        if TRANSFORMERS_AVAILABLE:
            try:
                self.tokenizer = AutoTokenizer.from_pretrained(model_name)
                self.generator = pipeline(
                    "text-generation",
                    model=model_name,
                    tokenizer=self.tokenizer,
                    device=0 if torch.cuda.is_available() else -1
                )
                if self.tokenizer.pad_token is None:
                    self.tokenizer.pad_token = self.tokenizer.eos_token
            except Exception as e:
                logger.error(f"Failed to load local model {model_name}: {e}")
    
    def is_available(self) -> bool:
        return self.generator is not None
    
    def generate_comment(self, request: CommentRequest) -> Optional[CommentResponse]:
        if not self.is_available():
            return None
        
        try:
            import time
            start_time = time.time()
            
            # Build input text
            input_text = f"Post: {request.post_title}\nComment:"
            
            # Generate response
            outputs = self.generator(
                input_text,
                max_length=len(input_text.split()) + 50,
                num_return_sequences=1,
                temperature=request.temperature,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id
            )
            
            generated_text = outputs[0]['generated_text']
            comment_text = generated_text.split("Comment:")[-1].strip()
            
            # Clean up the comment
            comment_text = self._clean_generated_text(comment_text)
            
            generation_time = time.time() - start_time
            
            return CommentResponse(
                comment_text=comment_text,
                confidence_score=0.6,  # Local models may be less reliable
                backend_used="local_transformer",
                generation_time=generation_time,
                metadata={"model": self.model_name}
            )
            
        except Exception as e:
            logger.error(f"Local transformer generation error: {e}")
            return None
    
    def _clean_generated_text(self, text: str) -> str:
        """Clean and format generated text"""
        # Remove special tokens and artifacts
        text = re.sub(r'<\|.*?\|>', '', text)
        text = re.sub(r'\[.*?\]', '', text)
        
        # Ensure proper sentence structure
        sentences = text.split('.')
        if len(sentences) > 1:
            text = '. '.join(sentences[:-1]) + '.'
        
        return text.strip()


class RuleBasedBackend(AIBackend):
    """Rule-based fallback backend"""
    
    def __init__(self, templates: Dict[str, List[str]]):
        self.templates = templates
    
    def is_available(self) -> bool:
        return True
    
    def generate_comment(self, request: CommentRequest) -> Optional[CommentResponse]:
        try:
            import time
            start_time = time.time()
            
            # Select appropriate template based on context
            template_category = self._select_template_category(request)
            templates = self.templates.get(template_category, self.templates.get('generic', []))
            
            if not templates:
                return None
            
            # Select random template
            template = random.choice(templates)
            
            # Fill template with context
            comment_text = self._fill_template(template, request)
            
            generation_time = time.time() - start_time
            
            return CommentResponse(
                comment_text=comment_text,
                confidence_score=0.4,  # Rule-based has lower confidence
                backend_used="rule_based",
                generation_time=generation_time,
                metadata={"template_category": template_category}
            )
            
        except Exception as e:
            logger.error(f"Rule-based generation error: {e}")
            return None
    
    def _select_template_category(self, request: CommentRequest) -> str:
        """Select appropriate template category"""
        if 'programming' in request.topics:
            return 'programming'
        elif 'career' in request.topics:
            return 'career'
        elif 'learning' in request.topics:
            return 'learning'
        elif '?' in request.post_title:
            return 'question'
        else:
            return 'generic'
    
    def _fill_template(self, template: str, request: CommentRequest) -> str:
        """Fill template with contextual information"""
        # Simple template filling
        filled = template.replace('{subreddit}', request.subreddit)
        filled = filled.replace('{topic}', ', '.join(request.topics) if request.topics else 'this topic')
        
        return filled


class CommentGenerator:
    """
    Main comment generator that orchestrates multiple AI backends
    """

    def __init__(self, config: Dict[str, Any], prompts_config: Dict[str, Any]):
        """
        Initialize comment generator with configuration

        Args:
            config: Main configuration dictionary
            prompts_config: Prompts configuration from prompts.yaml
        """
        self.config = config
        self.prompts_config = prompts_config
        self.backends = {}
        self.fallback_templates = self._load_fallback_templates()

        # Initialize AI backends
        self._initialize_backends()

    def _initialize_backends(self):
        """Initialize all available AI backends"""
        # Groq backend
        groq_api_key = os.getenv('GROQ_API_KEY')
        if groq_api_key:
            self.backends['groq'] = GroqBackend(groq_api_key)
            logger.info("Initialized Groq backend")

        # OpenAI backend
        openai_api_key = os.getenv('OPENAI_API_KEY')
        if openai_api_key:
            self.backends['openai'] = OpenAIBackend(openai_api_key)
            logger.info("Initialized OpenAI backend")

        # Local transformer backend
        if self.config.get('enable_local_ai', False):
            local_model = self.config.get('local_model', 'microsoft/DialoGPT-medium')
            self.backends['local'] = LocalTransformerBackend(local_model)
            logger.info("Initialized local transformer backend")

        # Rule-based fallback
        self.backends['rule_based'] = RuleBasedBackend(self.fallback_templates)
        logger.info("Initialized rule-based fallback backend")

    def _load_fallback_templates(self) -> Dict[str, List[str]]:
        """Load fallback comment templates"""
        return {
            'programming': [
                "Great question! I've encountered similar issues with {topic}. Have you tried checking the documentation for the specific framework you're using?",
                "This is a common challenge in {topic}. One approach that often works is to break down the problem into smaller components.",
                "I'd recommend looking into the official {topic} resources - they usually have good examples for this type of issue."
            ],
            'career': [
                "Thanks for sharing your experience! Career transitions can be challenging, but it sounds like you're taking the right steps.",
                "This is great advice for anyone looking to grow in {topic}. Networking and continuous learning are definitely key.",
                "I appreciate you sharing this perspective on {topic}. It's helpful to hear from someone with experience in the field."
            ],
            'learning': [
                "This is a great resource for learning {topic}! Thanks for sharing it with the community.",
                "I'm also learning {topic} and found this really helpful. Do you have any other recommendations?",
                "Excellent breakdown of {topic}! This kind of step-by-step explanation is exactly what beginners need."
            ],
            'question': [
                "Interesting question! While I don't have direct experience with this specific scenario, you might want to check the community wiki or search for similar discussions.",
                "Good question! This is something that comes up fairly often in r/{subreddit}. Have you tried the troubleshooting steps in the sidebar?",
                "Thanks for asking this - I was wondering about the same thing! Hopefully someone with more experience can chime in."
            ],
            'generic': [
                "Thanks for sharing this! It's always interesting to see different perspectives on {topic}.",
                "Great post! This kind of discussion is what makes r/{subreddit} such a valuable community.",
                "Appreciate you taking the time to write this up. Very informative and well-explained!"
            ]
        }

    def generate_comment(self, post_data, analysis_result, subreddit_config: Dict[str, Any]) -> Optional[CommentResponse]:
        """
        Generate a comment for a Reddit post

        Args:
            post_data: PostData object with post information
            analysis_result: AnalysisResult from content analysis
            subreddit_config: Subreddit-specific configuration

        Returns:
            CommentResponse or None if generation fails
        """
        try:
            # Build comment request
            request = self._build_comment_request(post_data, analysis_result, subreddit_config)

            # Get backend priority order
            backend_order = self._get_backend_order()

            # Try each backend in order
            for backend_name in backend_order:
                backend = self.backends.get(backend_name)
                if backend and backend.is_available():
                    logger.info(f"Attempting comment generation with {backend_name}")

                    response = backend.generate_comment(request)
                    if response and self._validate_comment(response.comment_text, request):
                        logger.info(f"Successfully generated comment with {backend_name}")
                        return response
                    else:
                        logger.warning(f"Comment validation failed for {backend_name}")

            logger.error("All backends failed to generate a valid comment")
            return None

        except Exception as e:
            logger.error(f"Error in comment generation: {e}")
            return None

    def _build_comment_request(self, post_data, analysis_result, subreddit_config: Dict[str, Any]) -> CommentRequest:
        """Build comment request from post data and analysis"""
        comment_style = subreddit_config.get('comment_style', 'helpful')
        max_length = self.config.get('max_comment_length', 500)
        temperature = self.config.get('temperature', 0.7)

        return CommentRequest(
            post_title=post_data.title,
            post_content=post_data.selftext,
            subreddit=post_data.subreddit,
            comment_style=comment_style,
            topics=analysis_result.topics,
            context={
                'post_score': post_data.score,
                'post_age': analysis_result.analysis_metadata.get('post_age_hours', 0),
                'sentiment': analysis_result.sentiment_label,
                'keywords': analysis_result.keywords
            },
            max_length=max_length,
            temperature=temperature
        )

    def _get_backend_order(self) -> List[str]:
        """Get backend priority order from configuration"""
        primary = self.config.get('primary_backend', 'groq')
        fallbacks = self.config.get('fallback_backends', ['local', 'rule_based'])

        # Build ordered list
        backend_order = [primary] + [b for b in fallbacks if b != primary]

        # Ensure rule_based is always last as ultimate fallback
        if 'rule_based' in backend_order:
            backend_order.remove('rule_based')
        backend_order.append('rule_based')

        return backend_order

    def _validate_comment(self, comment_text: str, request: CommentRequest) -> bool:
        """Validate generated comment meets quality standards"""
        if not comment_text or not comment_text.strip():
            return False

        # Length checks
        min_length = self.config.get('min_comment_length', 50)
        max_length = self.config.get('max_comment_length', 500)

        if len(comment_text) < min_length or len(comment_text) > max_length:
            return False

        # Content quality checks
        if self._contains_inappropriate_content(comment_text):
            return False

        # Ensure it's not too repetitive or template-like
        if self._is_too_generic(comment_text):
            return False

        return True

    def _contains_inappropriate_content(self, text: str) -> bool:
        """Check for inappropriate content in generated comment"""
        inappropriate_patterns = [
            r'\b(spam|scam|click here|buy now|free money)\b',
            r'\b(hate|stupid|idiot|moron)\b',
            r'\b(politics|religion|controversial)\b'
        ]

        text_lower = text.lower()
        for pattern in inappropriate_patterns:
            if re.search(pattern, text_lower):
                return True

        return False

    def _is_too_generic(self, text: str) -> bool:
        """Check if comment is too generic or template-like"""
        generic_phrases = [
            "thanks for sharing",
            "great post",
            "interesting question",
            "good point"
        ]

        text_lower = text.lower()
        generic_count = sum(1 for phrase in generic_phrases if phrase in text_lower)

        # If more than half the comment is generic phrases, it's too generic
        return generic_count > len(text.split()) / 10

    def get_backend_status(self) -> Dict[str, bool]:
        """Get status of all backends"""
        return {name: backend.is_available() for name, backend in self.backends.items()}
