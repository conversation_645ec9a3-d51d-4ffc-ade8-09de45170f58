"""
Content Analysis Module for Reddit Posts

This module provides sophisticated content analysis capabilities including
topic detection, sentiment analysis, and relevance scoring for Reddit posts.
"""

import re
import time
import nltk
from textblob import TextBlob
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sentence_transformers import SentenceTransformer
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from loguru import logger
import os


@dataclass
class AnalysisResult:
    """Results from content analysis"""
    relevance_score: float
    sentiment_score: float
    sentiment_label: str
    topics: List[str]
    keywords: List[str]
    readability_score: float
    engagement_potential: float
    content_quality: float
    should_comment: bool
    analysis_metadata: Dict[str, Any]


class ContentAnalyzer:
    """
    Advanced content analyzer for Reddit posts using NLP and ML techniques
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize content analyzer with configuration
        
        Args:
            config: Configuration dictionary for analysis parameters
        """
        self.config = config
        self.sentence_model = None
        self.tfidf_vectorizer = None
        self.topic_keywords = self._load_topic_keywords()
        self.negative_keywords = self._load_negative_keywords()
        
        # Initialize NLP models
        self._initialize_models()
        
        # Download required NLTK data
        self._download_nltk_data()
    
    def _initialize_models(self):
        """Initialize ML models for content analysis"""
        try:
            # Load sentence transformer for semantic similarity
            model_name = self.config.get('sentence_model', 'all-MiniLM-L6-v2')
            self.sentence_model = SentenceTransformer(model_name)
            logger.info(f"Loaded sentence transformer model: {model_name}")
            
            # Initialize TF-IDF vectorizer
            self.tfidf_vectorizer = TfidfVectorizer(
                max_features=1000,
                stop_words='english',
                ngram_range=(1, 2),
                min_df=1,
                max_df=0.95
            )
            
        except Exception as e:
            logger.error(f"Error initializing models: {e}")
            # Fallback to basic analysis without advanced models
            self.sentence_model = None
    
    def _download_nltk_data(self):
        """Download required NLTK data"""
        try:
            nltk.download('punkt', quiet=True)
            nltk.download('stopwords', quiet=True)
            nltk.download('vader_lexicon', quiet=True)
        except Exception as e:
            logger.warning(f"Could not download NLTK data: {e}")
    
    def _load_topic_keywords(self) -> Dict[str, List[str]]:
        """Load topic-specific keywords for relevance detection"""
        return {
            'programming': [
                'python', 'javascript', 'java', 'code', 'programming', 'development',
                'software', 'algorithm', 'debugging', 'framework', 'library', 'api',
                'database', 'frontend', 'backend', 'fullstack', 'devops'
            ],
            'machine_learning': [
                'machine learning', 'deep learning', 'neural network', 'ai', 'model',
                'training', 'dataset', 'tensorflow', 'pytorch', 'sklearn', 'data science',
                'regression', 'classification', 'clustering', 'nlp', 'computer vision'
            ],
            'web_development': [
                'html', 'css', 'react', 'vue', 'angular', 'node.js', 'express',
                'django', 'flask', 'responsive', 'bootstrap', 'webpack', 'npm',
                'yarn', 'sass', 'less', 'typescript', 'javascript'
            ],
            'career': [
                'job', 'career', 'interview', 'resume', 'salary', 'promotion',
                'skills', 'experience', 'hiring', 'recruiter', 'portfolio',
                'networking', 'linkedin', 'freelance', 'remote work'
            ],
            'learning': [
                'tutorial', 'course', 'learning', 'beginner', 'guide', 'documentation',
                'book', 'resource', 'practice', 'exercise', 'project', 'bootcamp',
                'certification', 'online course', 'udemy', 'coursera'
            ]
        }
    
    def _load_negative_keywords(self) -> List[str]:
        """Load keywords that indicate content to avoid"""
        return [
            'spam', 'scam', 'clickbait', 'fake', 'misleading', 'controversial',
            'offensive', 'inappropriate', 'nsfw', 'politics', 'religion',
            'hate', 'discrimination', 'illegal', 'piracy', 'copyright'
        ]
    
    def analyze_post(self, post_data, subreddit_config: Dict[str, Any]) -> AnalysisResult:
        """
        Perform comprehensive analysis of a Reddit post
        
        Args:
            post_data: PostData object containing post information
            subreddit_config: Subreddit-specific configuration
        
        Returns:
            AnalysisResult with comprehensive analysis
        """
        try:
            # Combine title and content for analysis
            full_text = f"{post_data.title} {post_data.selftext}".strip()
            
            # Basic content quality checks
            if not self._passes_basic_checks(post_data, subreddit_config):
                return self._create_negative_result("Failed basic quality checks")
            
            # Sentiment analysis
            sentiment_score, sentiment_label = self._analyze_sentiment(full_text)
            
            # Topic detection
            topics = self._detect_topics(full_text, subreddit_config.get('topics_of_interest', []))
            
            # Keyword extraction
            keywords = self._extract_keywords(full_text)
            
            # Relevance scoring
            relevance_score = self._calculate_relevance_score(
                full_text, topics, subreddit_config
            )
            
            # Readability assessment
            readability_score = self._assess_readability(full_text)
            
            # Engagement potential
            engagement_potential = self._calculate_engagement_potential(post_data)
            
            # Overall content quality
            content_quality = self._assess_content_quality(
                post_data, sentiment_score, readability_score
            )
            
            # Decision logic
            should_comment = self._should_comment_decision(
                relevance_score, sentiment_score, content_quality,
                engagement_potential, subreddit_config
            )
            
            return AnalysisResult(
                relevance_score=relevance_score,
                sentiment_score=sentiment_score,
                sentiment_label=sentiment_label,
                topics=topics,
                keywords=keywords,
                readability_score=readability_score,
                engagement_potential=engagement_potential,
                content_quality=content_quality,
                should_comment=should_comment,
                analysis_metadata={
                    'post_age_hours': (time.time() - post_data.created_utc) / 3600,
                    'post_length': len(full_text),
                    'has_selftext': bool(post_data.selftext.strip()),
                    'score_ratio': post_data.score / max(1, post_data.num_comments)
                }
            )
            
        except Exception as e:
            logger.error(f"Error analyzing post {post_data.id}: {e}")
            return self._create_negative_result(f"Analysis error: {e}")
    
    def _passes_basic_checks(self, post_data, subreddit_config: Dict[str, Any]) -> bool:
        """Check if post passes basic quality filters"""
        # Check if post is deleted, removed, or locked
        if post_data.deleted or post_data.removed or post_data.locked:
            return False
        
        # Check NSFW filter
        if post_data.over_18 and subreddit_config.get('skip_nsfw_posts', True):
            return False
        
        # Check minimum score
        min_score = subreddit_config.get('min_post_score', 5)
        if post_data.score < min_score:
            return False
        
        # Check post age
        post_age_hours = (time.time() - post_data.created_utc) / 3600
        min_age = subreddit_config.get('min_post_age_minutes', 5) / 60
        max_age = subreddit_config.get('max_post_age_hours', 24)
        
        if post_age_hours < min_age or post_age_hours > max_age:
            return False
        
        # Check minimum content length
        full_text = f"{post_data.title} {post_data.selftext}".strip()
        min_length = subreddit_config.get('min_post_length', 50)
        if len(full_text) < min_length:
            return False
        
        return True
    
    def _analyze_sentiment(self, text: str) -> Tuple[float, str]:
        """Analyze sentiment of text content"""
        try:
            blob = TextBlob(text)
            sentiment_score = blob.sentiment.polarity  # Range: -1 (negative) to 1 (positive)
            
            if sentiment_score > 0.1:
                sentiment_label = "positive"
            elif sentiment_score < -0.1:
                sentiment_label = "negative"
            else:
                sentiment_label = "neutral"
            
            return sentiment_score, sentiment_label
            
        except Exception as e:
            logger.warning(f"Error in sentiment analysis: {e}")
            return 0.0, "neutral"
    
    def _detect_topics(self, text: str, interests: List[str]) -> List[str]:
        """Detect relevant topics in the text"""
        text_lower = text.lower()
        detected_topics = []
        
        # Check against topic keywords
        for topic, keywords in self.topic_keywords.items():
            for keyword in keywords:
                if keyword.lower() in text_lower:
                    if topic not in detected_topics:
                        detected_topics.append(topic)
                    break
        
        # Check against subreddit-specific interests
        for interest in interests:
            if interest.lower() in text_lower:
                if interest not in detected_topics:
                    detected_topics.append(interest)
        
        return detected_topics
    
    def _extract_keywords(self, text: str, max_keywords: int = 10) -> List[str]:
        """Extract important keywords from text"""
        try:
            # Simple keyword extraction using word frequency
            words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
            
            # Filter out common stop words
            stop_words = {'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'man', 'way', 'she', 'use', 'her', 'many', 'oil', 'sit', 'word', 'but', 'not', 'what', 'all', 'were', 'they', 'we', 'when', 'your', 'said', 'each', 'which', 'their', 'time', 'will', 'about', 'if', 'up', 'out', 'many', 'then', 'them', 'these', 'so', 'some', 'her', 'would', 'make', 'like', 'into', 'him', 'has', 'two', 'more', 'very', 'what', 'know', 'just', 'first', 'get', 'over', 'think', 'also', 'your', 'work', 'life', 'only', 'can', 'still', 'should', 'after', 'being', 'now', 'made', 'before', 'here', 'through', 'when', 'where', 'much', 'go', 'me', 'back', 'with', 'well', 'were'}
            
            filtered_words = [word for word in words if word not in stop_words and len(word) > 3]
            
            # Count frequency
            word_freq = {}
            for word in filtered_words:
                word_freq[word] = word_freq.get(word, 0) + 1
            
            # Sort by frequency and return top keywords
            sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
            return [word for word, freq in sorted_words[:max_keywords]]
            
        except Exception as e:
            logger.warning(f"Error extracting keywords: {e}")
            return []

    def _calculate_relevance_score(self, text: str, topics: List[str],
                                 subreddit_config: Dict[str, Any]) -> float:
        """Calculate relevance score based on topics and interests"""
        try:
            interests = subreddit_config.get('topics_of_interest', [])
            avoid_topics = subreddit_config.get('avoid_topics', [])

            # Base score
            relevance_score = 0.0

            # Boost for matching interests
            for interest in interests:
                if interest.lower() in text.lower():
                    relevance_score += 0.3

            # Boost for detected topics
            for topic in topics:
                if topic in interests:
                    relevance_score += 0.2

            # Penalty for topics to avoid
            for avoid_topic in avoid_topics:
                if avoid_topic.lower() in text.lower():
                    relevance_score -= 0.5

            # Penalty for negative keywords
            for neg_keyword in self.negative_keywords:
                if neg_keyword.lower() in text.lower():
                    relevance_score -= 0.3

            # Normalize to 0-1 range
            return max(0.0, min(1.0, relevance_score))

        except Exception as e:
            logger.warning(f"Error calculating relevance score: {e}")
            return 0.0

    def _assess_readability(self, text: str) -> float:
        """Assess readability of the text content"""
        try:
            if not text.strip():
                return 0.0

            # Simple readability metrics
            sentences = text.split('.')
            words = text.split()

            if len(sentences) == 0 or len(words) == 0:
                return 0.0

            avg_sentence_length = len(words) / len(sentences)
            avg_word_length = sum(len(word) for word in words) / len(words)

            # Readability score (higher is better, normalized to 0-1)
            # Prefer moderate sentence and word lengths
            sentence_score = 1.0 - abs(avg_sentence_length - 15) / 30
            word_score = 1.0 - abs(avg_word_length - 5) / 10

            readability = (sentence_score + word_score) / 2
            return max(0.0, min(1.0, readability))

        except Exception as e:
            logger.warning(f"Error assessing readability: {e}")
            return 0.5

    def _calculate_engagement_potential(self, post_data) -> float:
        """Calculate potential for engagement based on post metrics"""
        try:
            # Factors that indicate good engagement potential
            score_factor = min(1.0, post_data.score / 50)  # Normalize score
            comment_factor = min(1.0, post_data.num_comments / 20)  # Normalize comments

            # Recent posts have higher engagement potential
            post_age_hours = (time.time() - post_data.created_utc) / 3600
            age_factor = max(0.0, 1.0 - post_age_hours / 24)  # Decay over 24 hours

            # Self posts often have better engagement
            self_post_bonus = 0.2 if post_data.is_self else 0.0

            engagement_potential = (score_factor + comment_factor + age_factor) / 3 + self_post_bonus
            return max(0.0, min(1.0, engagement_potential))

        except Exception as e:
            logger.warning(f"Error calculating engagement potential: {e}")
            return 0.5

    def _assess_content_quality(self, post_data, sentiment_score: float,
                              readability_score: float) -> float:
        """Assess overall content quality"""
        try:
            # Length factor (prefer substantial content)
            full_text = f"{post_data.title} {post_data.selftext}".strip()
            length_factor = min(1.0, len(full_text) / 500)

            # Sentiment factor (prefer neutral to positive)
            sentiment_factor = 0.5 + sentiment_score * 0.5 if sentiment_score >= 0 else 0.3

            # Title quality (prefer descriptive titles)
            title_factor = min(1.0, len(post_data.title.split()) / 10)

            # Combine factors
            quality_score = (length_factor + sentiment_factor + readability_score + title_factor) / 4
            return max(0.0, min(1.0, quality_score))

        except Exception as e:
            logger.warning(f"Error assessing content quality: {e}")
            return 0.5

    def _should_comment_decision(self, relevance_score: float, sentiment_score: float,
                               content_quality: float, engagement_potential: float,
                               subreddit_config: Dict[str, Any]) -> bool:
        """Make final decision on whether to comment"""
        try:
            # Minimum thresholds
            min_relevance = subreddit_config.get('min_relevance_score', 0.6)
            min_quality = 0.4
            min_engagement = 0.3

            # Check minimum requirements
            if relevance_score < min_relevance:
                return False

            if content_quality < min_quality:
                return False

            if engagement_potential < min_engagement:
                return False

            # Avoid very negative content
            if sentiment_score < -0.5:
                return False

            # Calculate overall score
            overall_score = (relevance_score * 0.4 + content_quality * 0.3 +
                           engagement_potential * 0.3)

            return overall_score >= 0.6

        except Exception as e:
            logger.warning(f"Error in comment decision: {e}")
            return False

    def _create_negative_result(self, reason: str) -> AnalysisResult:
        """Create a negative analysis result"""
        return AnalysisResult(
            relevance_score=0.0,
            sentiment_score=0.0,
            sentiment_label="neutral",
            topics=[],
            keywords=[],
            readability_score=0.0,
            engagement_potential=0.0,
            content_quality=0.0,
            should_comment=False,
            analysis_metadata={'rejection_reason': reason}
        )
