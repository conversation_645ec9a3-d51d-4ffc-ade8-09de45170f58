# Reddit API Credentials
# Get these from https://www.reddit.com/prefs/apps
REDDIT_CLIENT_ID=your_client_id_here
REDDIT_CLIENT_SECRET=your_client_secret_here
REDDIT_USER_AGENT=RedditSage/1.0 by YourUsername
REDDIT_USERNAME=your_reddit_username
REDDIT_PASSWORD=your_reddit_password

# AI API Keys (Choose one or more)
# Groq API (Free tier available)
GROQ_API_KEY=your_groq_api_key_here

# OpenAI API (Paid)
OPENAI_API_KEY=your_openai_api_key_here

# Together AI (Free credits available)
TOGETHER_API_KEY=your_together_api_key_here

# Anthropic Claude (Free tier available)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Database Configuration
DATABASE_PATH=data/comments.db

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=data/logs/reddit_bot.log

# Rate Limiting
MAX_COMMENTS_PER_HOUR=5
MAX_COMMENTS_PER_DAY=50
MIN_COMMENT_INTERVAL_MINUTES=10

# Bot Behavior
DRY_RUN=false
ENABLE_AI_COMMENTS=true
ENABLE_LOCAL_AI=false
COMMENT_MIN_LENGTH=50
COMMENT_MAX_LENGTH=500

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=8080

# Commercial Features
COMMERCIAL_LICENSE_KEY=your_license_key_here
ENABLE_PREMIUM_FEATURES=false
ANALYTICS_ENDPOINT=https://api.redditsage.ai/analytics
SUPPORT_EMAIL=<EMAIL>

# Performance Optimization
MAX_CONCURRENT_REQUESTS=5
REQUEST_TIMEOUT_SECONDS=30
CACHE_TTL_SECONDS=3600
ENABLE_REQUEST_CACHING=true

# Security
ENABLE_API_RATE_LIMITING=true
API_RATE_LIMIT_PER_MINUTE=60
ENABLE_IP_WHITELIST=false
ALLOWED_IPS=127.0.0.1,::1

# Advanced Features
ENABLE_SENTIMENT_ANALYSIS=true
ENABLE_TOPIC_MODELING=true
ENABLE_CONTENT_MODERATION=true
ENABLE_SPAM_DETECTION=true
ENABLE_KARMA_OPTIMIZATION=true
